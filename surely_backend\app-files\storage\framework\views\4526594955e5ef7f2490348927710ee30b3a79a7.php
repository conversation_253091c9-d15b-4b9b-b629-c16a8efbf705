

<?php $__env->startSection('content'); ?>
<div class="layout-px-spacing">
    <div class="row layout-top-spacing">
        <div class="col-xl-12 col-lg-12 col-sm-12 layout-spacing">
            <div class="widget-content widget-content-area br-6">
                <div class="invoice-container">
                    <div class="row">
                        <div class="col-xl-12">
                            <div class="invoice-content">
                                <div class="invoice-detail-header">
                                    <div class="row justify-content-between">
                                        <div class="col-xl-5 invoice-address-company">
                                            <h4>Invoice</h4>
                                            <div class="invoice-address-company-fields">
                                                <div class="form-group row mb-2">
                                                    <label class="col-sm-4 col-form-label col-form-label-sm font-weight-bold text-left">Invoice ID</label>
                                                    <div class="col-sm-8">
                                                        <p class="mb-0 pt-1"><?php echo e($invoice->id); ?></p>
                                                    </div>
                                                </div>
                                                <div class="form-group row mb-2">
                                                    <label class="col-sm-4 col-form-label col-form-label-sm font-weight-bold text-left">Contract ID</label>
                                                    <div class="col-sm-8">
                                                        <p class="mb-0 pt-1"><?php echo e($contract->id); ?></p>
                                                    </div>
                                                </div>
                                                <div class="form-group row mb-2">
                                                    <label class="col-sm-4 col-form-label col-form-label-sm font-weight-bold text-left">Type</label>
                                                    <div class="col-sm-8">
                                                        <p class="mb-0 pt-1"><span class="badge <?php echo e($invoice->type == 'escrow' ? 'badge-info' : 'badge-primary'); ?>"><?php echo e(ucfirst($invoice->type)); ?></span></p>
                                                    </div>
                                                </div>
                                                <div class="form-group row mb-2">
                                                    <label class="col-sm-4 col-form-label col-form-label-sm font-weight-bold text-left">Status</label>
                                                    <div class="col-sm-8">
                                                        <p class="mb-0 pt-1">
                                                            <span class="badge <?php echo e($invoice->status == 'pending' ? 'badge-warning' : 'badge-success'); ?>"><?php echo e(ucfirst($invoice->status)); ?></span>
                                                        </p>
                                                    </div>
                                                </div>
                                                <div class="form-group row mb-2">
                                                    <label class="col-sm-4 col-form-label col-form-label-sm font-weight-bold text-left">Payment Status</label>
                                                    <div class="col-sm-8">
                                                        <p class="mb-0 pt-1">
                                                            <span class="badge <?php echo e($payment_status == 'paid' ? 'badge-success' : 'badge-warning'); ?>"><?php echo e(ucfirst($payment_status)); ?></span>
                                                        </p>
                                                    </div>
                                                </div>
                                                <div class="form-group row mb-2">
                                                    <label class="col-sm-4 col-form-label col-form-label-sm font-weight-bold text-left">Issue Date</label>
                                                    <div class="col-sm-8">
                                                        <p class="mb-0 pt-1"><?php echo e($issueDate); ?></p>
                                                    </div>
                                                </div>
                                                <div class="form-group row mb-2">
                                                    <label class="col-sm-4 col-form-label col-form-label-sm font-weight-bold text-left">Due Date</label>
                                                    <div class="col-sm-8">
                                                        <p class="mb-0 pt-1"><?php echo e($dueDate); ?></p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-xl-5 invoice-address-client">
                                            <h4>Bill To:</h4>
                                            <div class="invoice-address-client-fields">
                                                <div class="form-group row mb-2">
                                                    <label class="col-sm-4 col-form-label col-form-label-sm font-weight-bold text-left">Name</label>
                                                    <div class="col-sm-8">
                                                        <p class="mb-0 pt-1"><?php echo e($contract->client->name); ?></p>
                                                    </div>
                                                </div>
                                                <div class="form-group row mb-2">
                                                    <label class="col-sm-4 col-form-label col-form-label-sm font-weight-bold text-left">Email</label>
                                                    <div class="col-sm-8">
                                                        <p class="mb-0 pt-1"><?php echo e($contract->client->email); ?></p>
                                                    </div>
                                                </div>
                                                <div class="form-group row mb-2">
                                                    <label class="col-sm-4 col-form-label col-form-label-sm font-weight-bold text-left">Phone</label>
                                                    <div class="col-sm-8">
                                                        <p class="mb-0 pt-1"><?php echo e($contract->client->phone ?? 'N/A'); ?></p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="invoice-detail-items">
                                    <div class="table-responsive">
                                        <table class="table table-bordered item-table">
                                            <thead>
                                                <tr>
                                                    <th>Description</th>
                                                    <th>Type</th>
                                                    <th>Sub Amount</th>
                                                    <th>Surely fees (Fee + VAT)</th>
                                                    <?php if($contract->job && $contract->job->is_emergency_hire): ?>
                                                        <th>Emergency Hire Fee (<?php echo e($contract->emergency_hire_fee_rate); ?>%)</th>
                                                    <?php endif; ?>
                                                    <th>Total</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td><?php echo e($invoice->description ?: ($invoice->type == 'escrow' ? 'Escrow Payment' : 'Outstanding Payment')); ?></td>
                                                    <td>
                                                        <span class="badge <?php echo e($invoice->type == 'escrow' ? 'badge-info' : 'badge-primary'); ?>"><?php echo e(ucfirst($invoice->type)); ?></span>
                                                    </td>
                                                    <td><?php echo e($invoice->currency); ?> <?php echo e(number_format($invoice->sub_amount, 2)); ?></td>
                                                    <td><?php echo e($invoice->currency); ?> <?php echo e(number_format($invoice->amount - $invoice->sub_amount, 2)); ?></td>
                                                    <?php if($contract->job && $contract->job->is_emergency_hire): ?>
                                                        <td><?php echo e($invoice->currency); ?> <?php echo e($invoice->type == 'escrow' ? number_format($contract->emergency_hire_fee_amount, 2) : '0.00'); ?></td>
                                                    <?php endif; ?>
                                                    <td><?php echo e($invoice->currency); ?> <?php echo e(number_format($invoice->amount, 2)); ?></td>
                                                </tr>
                                                <?php if($invoice->type == 'escrow' && $invoiceOutstanding): ?>
                                                <tr>
                                                    <td>Outstanding Payment</td>
                                                    <td><span class="badge badge-primary">Payment</span></td>
                                                    <td><?php echo e($invoiceOutstanding->currency); ?> <?php echo e(number_format($invoiceOutstanding->sub_amount, 2)); ?></td>
                                                    <td><?php echo e($invoiceOutstanding->currency); ?> <?php echo e(number_format($invoiceOutstanding->amount - $invoiceOutstanding->sub_amount, 2)); ?></td>
                                                    <?php if($contract->job && $contract->job->is_emergency_hire): ?>
                                                        <td><?php echo e($invoiceOutstanding->currency); ?> 0.00</td>
                                                    <?php endif; ?>
                                                    <td><?php echo e($invoiceOutstanding->currency); ?> <?php echo e(number_format($invoiceOutstanding->amount, 2)); ?></td>
                                                </tr>
                                                <?php elseif($invoice->type == 'payment' && $invoiceEscrow): ?>
                                                <tr>
                                                    <td>Escrow Payment</td>
                                                    <td><span class="badge badge-info">Escrow</span></td>
                                                    <td><?php echo e($invoiceEscrow->currency); ?> <?php echo e(number_format($invoiceEscrow->sub_amount, 2)); ?></td>
                                                    <td><?php echo e($invoiceEscrow->currency); ?> <?php echo e(number_format($invoiceEscrow->amount - $invoiceEscrow->sub_amount, 2)); ?></td>
                                                    <?php if($contract->job && $contract->job->is_emergency_hire): ?>
                                                        <td><?php echo e($invoiceEscrow->currency); ?> 0.00</td>
                                                    <?php endif; ?>
                                                    <td><?php echo e($invoiceEscrow->currency); ?> <?php echo e(number_format($invoiceEscrow->amount, 2)); ?></td>
                                                </tr>
                                                <?php endif; ?>
                                                <!-- Total Row -->
                                                <?php
                                                    // Calculate total contract value from both invoices
                                                    $escrowTotal = $invoice->type === 'escrow' ? $invoice->amount : ($invoiceEscrow ? $invoiceEscrow->amount : 0);
                                                    $outstandingTotal = $invoice->type === 'payment' ? $invoice->amount : ($invoiceOutstanding ? $invoiceOutstanding->amount : 0);
                                                    $totalContractValue = $escrowTotal + $outstandingTotal;
                                                ?>
                                                <tr class="font-weight-bold">
                                                    <td colspan="<?php echo e(($contract->job && $contract->job->is_emergency_hire) ? '5' : '4'); ?>" class="text-right">Total Contract Value</td>
                                                    <td><?php echo e($invoice->currency); ?> <?php echo e(number_format($totalContractValue, 2)); ?></td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>

                                <div class="invoice-detail-total mt-4">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="card border mb-3">
                                                <div class="card-header bg-light">
                                                    <h5 class="mb-0">Contract Details</h5>
                                                </div>
                                                <div class="card-body pt-2">
                                                    <div class="form-group row mb-2">
                                                        <label class="col-sm-4 col-form-label col-form-label-sm font-weight-bold text-left">Contract ID</label>
                                                        <div class="col-sm-8">
                                                            <p class="mb-0 pt-1"><?php echo e($contract->id); ?></p>
                                                        </div>
                                                    </div>

                                                    <div class="form-group row mb-2">
                                                        <label class="col-sm-4 col-form-label col-form-label-sm font-weight-bold text-left">Payment Status</label>
                                                        <div class="col-sm-8">
                                                            <p class="mb-0 pt-1">
                                                                <?php
                                                                // Calculate total paid amount
                                                                $escrowAmount = $invoice->type == 'escrow' ? $invoice->amount : ($invoiceEscrow ? $invoiceEscrow->amount : 0);
                                                                $escrowPaid = $invoice->type == 'escrow' ? ($invoice->payment_status == 'paid') : ($invoiceEscrow && $invoiceEscrow->payment_status == 'paid');

                                                                $regularAmount = $invoice->type == 'payment' ? $invoice->amount : ($invoiceOutstanding ? $invoiceOutstanding->amount : 0);
                                                                $regularPaid = $invoice->type == 'payment' ? ($invoice->payment_status == 'paid') : ($invoiceOutstanding && $invoiceOutstanding->payment_status == 'paid');
                                                                
                                                                $totalPaidAmount = 0;
                                                                if ($escrowPaid) $totalPaidAmount += $escrowAmount;
                                                                if ($regularPaid) $totalPaidAmount += $regularAmount;
                                                                
                                                                $totalContractAmount = $contract->total_amount;
                                                                $percentPaid = $totalContractAmount > 0 ? round(($totalPaidAmount / $totalContractAmount) * 100) : 0;
                                                                
                                                                // Determine status
                                                                $badgeClass = 'badge-danger';
                                                                $statusText = 'Not Paid';
                                                                
                                                                if ($percentPaid >= 99) { // Allow for rounding errors
                                                                    $badgeClass = 'badge-success';
                                                                    $statusText = 'Fully Paid';
                                                                } elseif ($percentPaid > 0) {
                                                                    $badgeClass = 'badge-warning';
                                                                    $statusText = 'Partially Paid (' . $percentPaid . '%)';
                                                                }
                                                                ?>
                                                                <span class="badge <?php echo e($badgeClass); ?>"><?php echo e($statusText); ?></span>
                                                            </p>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="card border">
                                                <div class="card-header bg-light">
                                                    <h5 class="mb-0">Operative Details</h5>
                                                </div>
                                                <div class="card-body pt-2">
                                                    <div class="form-group row mb-2">
                                                        <label class="col-sm-4 col-form-label col-form-label-sm font-weight-bold text-left">Name</label>
                                                        <div class="col-sm-8">
                                                            <p class="mb-0 pt-1"><?php echo e($contract->operative->name); ?></p>
                                                        </div>
                                                    </div>
                                                    <div class="form-group row mb-2">
                                                        <label class="col-sm-4 col-form-label col-form-label-sm font-weight-bold text-left">Email</label>
                                                        <div class="col-sm-8">
                                                            <p class="mb-0 pt-1"><?php echo e($contract->operative->email); ?></p>
                                                        </div>
                                                    </div>
                                                    <div class="form-group row mb-2">
                                                        <label class="col-sm-4 col-form-label col-form-label-sm font-weight-bold text-left">Phone</label>
                                                        <div class="col-sm-8">
                                                            <p class="mb-0 pt-1"><?php echo e($contract->operative->phone ?? 'N/A'); ?></p>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="card border h-100">
                                                <div class="card-header bg-light">
                                                    <h5 class="mb-0">Payment Summary</h5>
                                                </div>
                                                <div class="card-body">
                                                    <?php
                                                    // Get escrow payment details
                                                    $escrowAmount = $invoice->type == 'escrow' ? $invoice->amount : ($invoiceEscrow ? $invoiceEscrow->amount : 0);
                                                    $escrowPaid = $invoice->type == 'escrow' ? ($invoice->payment_status == 'paid') : ($invoiceEscrow && $invoiceEscrow->payment_status == 'paid');
                                                    $escrowInvoiceId = $invoice->type == 'escrow' ? $invoice->id : ($invoiceEscrow ? $invoiceEscrow->id : null);
                                                    $escrowDate = $invoice->type == 'escrow' ? $invoice->created_at : ($invoiceEscrow ? $invoiceEscrow->created_at : null);
                                                    
                                                    // Get regular payment details
                                                    $regularAmount = $invoice->type == 'payment' ? $invoice->amount : ($invoiceOutstanding ? $invoiceOutstanding->amount : 0);
                                                    $regularPaid = $invoice->type == 'payment' ? ($invoice->payment_status == 'paid') : ($invoiceOutstanding && $invoiceOutstanding->payment_status == 'paid');
                                                    $regularInvoiceId = $invoice->type == 'payment' ? $invoice->id : ($invoiceOutstanding ? $invoiceOutstanding->id : null);
                                                    $regularDate = $invoice->type == 'payment' ? $invoice->created_at : ($invoiceOutstanding ? $invoiceOutstanding->created_at : null);
                                                    
                                                    // Calculate total paid amount
                                                    $totalPaidAmount = 0;
                                                    if ($escrowPaid) $totalPaidAmount += $escrowAmount;
                                                    if ($regularPaid) $totalPaidAmount += $regularAmount;
                                                    
                                                    // Get contract total amount and calculate remaining amount
                                                    $totalContractAmount = $contract->total_amount;
                                                    $remainingAmount = $contract->total_amount - $totalPaidAmount;
                                                    $percentPaid = $contract->total_amount > 0 ? round(($totalPaidAmount / $contract->total_amount) * 100) : 0;
                                                    ?>
                                                    
                                                    <div class="table-responsive">
                                                        <table class="table table-sm">
                                                            <thead>
                                                                <tr>
                                                                    <th>Payment Type</th>
                                                                    <th class="text-right">Amount</th>
                                                                    <th class="text-center">Status</th>
                                                                    <th class="text-center">Date</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody>
                                                                <?php if($invoice->type == 'escrow' || $invoiceEscrow): ?>
                                                                <tr>
                                                                    <td>
                                                                        <span class="badge badge-info">Escrow</span>
                                                                        <small class="d-block">
                                                                            Invoice #<?php echo e($escrowInvoiceId); ?>

                                                                        </small>
                                                                        <?php if($escrowAmount > 0 && $contract->total_amount > 0): ?>
                                                                        <small class="d-block text-muted">(<?php echo round(($escrowAmount / $contract->total_amount) * 100); ?>% of total)</small>
                                                                        <?php endif; ?>
                                                                    </td>
                                                                    <td class="text-right">
                                                                        <?php echo e($invoice->currency); ?> <?php echo e(number_format($escrowAmount, 2)); ?>

                                                                    </td>
                                                                    <td class="text-center">
                                                                        <span class="badge <?php echo e($escrowPaid ? 'badge-success' : 'badge-warning'); ?>">
                                                                            <?php echo e($escrowPaid ? 'Paid' : 'Pending'); ?>

                                                                        </span>
                                                                    </td>
                                                                    <td class="text-center">
                                                                        <?php echo e($escrowDate ? $escrowDate->format('d-m-Y') : 'N/A'); ?>

                                                                    </td>
                                                                </tr>
                                                                <?php endif; ?>
                                                                <?php if($invoice->type == 'payment' || $invoiceOutstanding): ?>
                                                                <tr>
                                                                    <td>
                                                                        <span class="badge badge-primary">Regular Payment</span>
                                                                        <small class="d-block">
                                                                            Invoice #<?php echo e($regularInvoiceId); ?>

                                                                        </small>
                                                                        <?php if($regularAmount > 0 && $contract->total_amount > 0): ?>
                                                                        <small class="d-block text-muted">(<?php echo round(($regularAmount / $contract->total_amount) * 100); ?>% of total)</small>
                                                                        <?php endif; ?>
                                                                    </td>
                                                                    <td class="text-right">
                                                                        <?php echo e($invoice->currency); ?> <?php echo e(number_format($regularAmount, 2)); ?>

                                                                    </td>
                                                                    <td class="text-center">
                                                                        <span class="badge <?php echo e($regularPaid ? 'badge-success' : 'badge-warning'); ?>">
                                                                            <?php echo e($regularPaid ? 'Paid' : 'Pending'); ?>

                                                                        </span>
                                                                    </td>
                                                                    <td class="text-center">
                                                                        <?php echo e($regularDate ? $regularDate->format('d-m-Y') : 'N/A'); ?>

                                                                    </td>
                                                                </tr>
                                                                <?php endif; ?>
                                                                
                                                                <!-- Summary rows -->
                                                                <tr>
                                                                    <td colspan="4" class="pt-3 border-top"></td>
                                                                </tr>
                                                                <tr>
                                                                    <td><strong>Total Paid</strong></td>
                                                                    <td class="text-right"><?php echo e($invoice->currency); ?> <?php echo e(number_format($totalPaidAmount, 2)); ?></td>
                                                                    <td colspan="2" class="text-center">
                                                                        <?php if($contract->total_amount > 0): ?>
                                                                        <div class="progress" style="height: 20px;">
                                                                            <div class="progress-bar bg-success" role="progressbar" style="width: <?php echo e($percentPaid); ?>%;" aria-valuenow="<?php echo e($percentPaid); ?>" aria-valuemin="0" aria-valuemax="100"><?php echo e($percentPaid); ?>%</div>
                                                                        </div>
                                                                        <?php endif; ?>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td><strong>Remaining Balance</strong></td>
                                                                    <td class="text-right"><?php echo e($invoice->currency); ?> <?php echo e(number_format($remainingAmount, 2)); ?></td>
                                                                    <td colspan="2"></td>
                                                                </tr>
                                                                <tr class="font-weight-bold" style="border-top: 2px solid #dee2e6">
                                                                    <td colspan="<?php echo e(($contract->job && $contract->job->is_emergency_hire) ? '4' : '3'); ?>">Total Contract Value</td>
                                                                    <td><?php echo e($invoice->currency); ?> <?php echo e(number_format($contract->total_amount, 2)); ?></td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="invoice-detail-note">
                                    <div class="row">
                                        <div class="col-md-12 align-self-center">
                                            <div class="form-group row invoice-note">
                                                <label class="col-sm-12 col-form-label col-form-label-sm">Note:</label>
                                                <div class="col-sm-12">
                                                    <p><?php echo e($contract->description ?? 'No additional notes.'); ?></p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mt-4 no-print-buttons">
                        <div class="col-md-12">
                            <div class="invoice-actions-btn">
                                <div class="invoice-action-btn">
                                    <a href="<?php echo e(route('invoices.index')); ?>" class="btn btn-primary btn-download">Back to Invoices</a>
                                    <a href="javascript:void(0)" onclick="printInvoice()" class="btn btn-success btn-print">Print</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>



<?php $__env->stopSection(); ?>

<?php $__env->startSection('styles'); ?>
<style>
    /* Print-optimized styles for A4 format */
    @media  print {
        /* Reset page layout for print */
        body {
            margin: 0 !important;
            padding: 0 !important;
            background: white !important;
            font-family: 'Rubik', Arial, sans-serif !important;
            font-size: 12px !important;
            line-height: 1.4 !important;
            color: #000 !important;
        }

        /* Hide admin navigation and layout elements */
        .layout-px-spacing {
            padding: 0 !important;
            margin: 0 !important;
        }

        .row {
            margin: 0 !important;
        }

        .widget-content {
            padding: 20mm !important;
            margin: 0 !important;
            box-shadow: none !important;
            border: none !important;
            border-radius: 0 !important;
        }

        /* Hide buttons ONLY during print */
        .invoice-actions-btn,
        .invoice-action-btn,
        .btn,
        .btn-primary,
        .btn-success,
        .btn-download,
        .btn-print,
        .badge,
        .progress,
        .no-print,
        .no-print-buttons {
            display: none !important;
            visibility: hidden !important;
        }

        /* Hide the entire button container row */
        .no-print-buttons,
        .no-print-buttons * {
            display: none !important;
            visibility: hidden !important;
        }

        /* Target specific button links */
        a[href*="invoices"],
        a[href*="print"],
        a[onclick*="print"],
        a[href="javascript:void(0)"] {
            display: none !important;
            visibility: hidden !important;
        }

        /* Hide the last row containing buttons */
        .invoice-container .row:last-child {
            display: none !important;
        }

        /* Format invoice header */
        .invoice-detail-header {
            margin-bottom: 30px !important;
            border-bottom: 2px solid #000 !important;
            padding-bottom: 20px !important;
        }

        .invoice-detail-header h4 {
            font-size: 24px !important;
            font-weight: bold !important;
            color: #000 !important;
            margin: 0 !important;
        }

        /* Format invoice content sections */
        .invoice-address-company,
        .invoice-address-client {
            margin-bottom: 20px !important;
        }

        .invoice-address-company h4,
        .invoice-address-client h4 {
            font-size: 16px !important;
            font-weight: bold !important;
            margin-bottom: 10px !important;
            color: #000 !important;
            border-bottom: 1px solid #ccc !important;
            padding-bottom: 5px !important;
        }

        /* Format form groups for clean display */
        .form-group {
            margin-bottom: 8px !important;
        }

        .col-form-label {
            font-weight: 600 !important;
            color: #333 !important;
            font-size: 12px !important;
        }

        .form-group p {
            color: #000 !important;
            font-size: 12px !important;
            margin: 0 !important;
        }

        /* Format tables */
        .table {
            font-size: 11px !important;
            margin: 20px 0 !important;
        }

        .table th {
            background-color: #f8f9fa !important;
            font-weight: bold !important;
            color: #000 !important;
            border: 1px solid #ddd !important;
            padding: 8px !important;
        }

        .table td {
            border: 1px solid #ddd !important;
            padding: 8px !important;
            color: #000 !important;
        }

        /* Format cards */
        .card {
            border: 1px solid #ddd !important;
            margin-bottom: 20px !important;
            box-shadow: none !important;
        }

        .card-header {
            background-color: #f8f9fa !important;
            border-bottom: 1px solid #ddd !important;
            padding: 10px !important;
        }

        .card-header h5 {
            font-size: 14px !important;
            font-weight: bold !important;
            color: #000 !important;
            margin: 0 !important;
        }

        .card-body {
            padding: 15px !important;
        }

        /* Format invoice notes */
        .invoice-note label {
            font-size: 14px !important;
            font-weight: bold !important;
            color: #000 !important;
        }

        .invoice-note p {
            font-size: 12px !important;
            color: #000 !important;
            margin-top: 5px !important;
        }

        /* Ensure proper page layout */
        .invoice-container {
            max-width: 210mm !important;
            margin: 0 auto !important;
            background: white !important;
        }

        /* Page break control */
        .invoice-detail-items,
        .invoice-detail-total {
            page-break-inside: avoid !important;
        }

        /* Remove any remaining admin styling */
        .layout-top-spacing,
        .layout-spacing {
            margin: 0 !important;
            padding: 0 !important;
        }
    }

    /* Screen styles remain unchanged */
    .invoice-detail-total .totals-row {
        border-top: 1px solid #e0e6ed;
        padding-top: 15px;
    }

    .invoice-totals-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 5px;
    }

    .invoice-summary-total {
        font-weight: bold;
        font-size: 1.125rem;
    }
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
function printInvoice() {
    // Store original title
    const originalTitle = document.title;

    // Set print-friendly title
    document.title = 'Invoice #<?php echo e($contract->id); ?> - Surely';

    // Trigger print
    window.print();

    // Restore original title after print dialog
    setTimeout(() => {
        document.title = originalTitle;
    }, 1000);
}

// Optional: Add keyboard shortcut for printing (Ctrl+P)
document.addEventListener('keydown', function(e) {
    if (e.ctrlKey && e.key === 'p') {
        e.preventDefault();
        printInvoice();
    }
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\resources\views/invoices/show.blade.php ENDPATH**/ ?>