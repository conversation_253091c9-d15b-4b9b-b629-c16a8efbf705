# Payment Process Documentation

## 📋 Overview

This document explains how the Surely platform handles payments between clients and operatives, including all fees and charges. The system uses an **escrow-based payment model** to ensure security and trust for both parties.

## 🔄 Payment Flow Summary

1. **Client posts a job** → **Operative applies** → **Contract created**
2. **Client pays escrow** (holds money safely)
3. **Operative completes work** → **Client confirms completion**
4. **Money released to operative** (minus platform fees)

## 💰 Fee Structure

### Base Calculation Rules
All fees are calculated using these standardized rates:

| Fee Type | Rate | Applied To | Purpose |
|----------|------|------------|---------|
| **Emergency Hire Fee** | 5% | Original Subtotal | Premium for urgent jobs |
| **Operative VAT** | 20% | Original Subtotal | VAT charged by VAT-registered operatives |
| **Surely Fee** | 20% | (Subtotal + Operative VAT + Emergency Hire) | Platform service fee |
| **Surely VAT** | 20% | Surely Fee | VAT on platform fees |

### Calculation Formula
```
1. Subtotal = Hourly Rate × Hours Worked
2. Emergency Hire Fee = 5% × Subtotal (if emergency job)
3. Operative VAT = 20% × Subtotal (if operative is VAT registered)
4. Surely Fee = 20% × (Subtotal + Operative VAT + Emergency Hire Fee)
5. Surely VAT = 20% × Surely Fee
6. Total = Subtotal + Emergency Hire Fee + Operative VAT + Surely Fee + Surely VAT
```

## 📊 Real Example Breakdown

Using the database entries you provided, here's how the payment was calculated:

### Job Details
- **Job**: Dog walker (Emergency hire)
- **Duration**: 10 hours (09:00 - 19:00)
- **Hourly Rate**: £10.00
- **Emergency Job**: Yes
- **Operative VAT Status**: Not VAT registered

### Payment Calculation
```
1. Subtotal: £10.00 × 10 hours = £100.00
2. Emergency Hire Fee: 5% × £100.00 = £5.00
3. Operative VAT: 0% × £100.00 = £0.00 (not VAT registered)
4. Surely Fee Base: £100.00 + £0.00 + £5.00 = £105.00
5. Surely Fee: 20% × £105.00 = £21.00
6. Surely VAT: 20% × £21.00 = £4.20
7. Total Amount: £100.00 + £5.00 + £0.00 + £21.00 + £4.20 = £130.20
```

### Database Storage
The system stores these calculated values in the database:

**Contracts Table:**
- `sub_total`: £100.00 (base payment)
- `emergency_hire_fee_amount`: £5.00
- `operator_vat_amount`: £0.00
- `application_fee_amount`: £21.00 (Surely Fee)
- `application_vat_amount`: £4.20 (Surely VAT)
- `total_amount`: £130.20

## 🏦 Escrow System

### How Escrow Works
1. **Client pays upfront** into a secure escrow account
2. **Money is held safely** until work is completed
3. **Operative completes work** and client confirms
4. **Money is released** to the operative

### Escrow Rates
- **Standard Jobs**: 100% escrow (full payment upfront)
- **Large Contracts**: May use partial escrow (e.g., 50%)

### Benefits
- **For Clients**: Guaranteed work completion
- **For Operatives**: Guaranteed payment
- **For Platform**: Reduced payment disputes

## 📄 Invoice Generation

### Invoice Types
1. **Escrow Invoice**: Generated when client pays upfront
2. **Payment Invoice**: Generated when money is released to operative

### Invoice Contents
Each invoice includes:
- Subtotal breakdown
- All applicable fees
- VAT calculations
- Payment terms
- Due dates

## 💳 Payment Methods

### Supported Payment Types
- Credit/Debit Cards (via Stripe)
- Bank transfers
- Digital wallets

### Payment Security
- All payments processed through Stripe
- PCI DSS compliant
- Encrypted transactions
- Fraud protection

## 🔄 Contract Status Flow

### Status Progression
```
1. Invited → 2. Pending → 3. In Progress → 4. Completed
```

### Payment Status Flow
```
Pending → Paid → (Refunded if needed)
```

### Escrow Status Flow
```
Pending → Paid → (Released to operative)
```

## 🎯 Key Business Benefits

### Revenue Generation
- **Surely Fee**: 20% of total job value (main revenue source)
- **Emergency Premium**: 5% additional for urgent jobs
- **VAT Collection**: 20% on platform fees

### Risk Mitigation
- **Escrow system** eliminates payment defaults
- **Upfront payment** ensures client commitment
- **Structured fees** provide predictable revenue

### User Experience
- **Transparent pricing** with clear fee breakdown
- **Secure payments** through established providers
- **Automated calculations** prevent errors

## 📈 Financial Impact Example

For the £100 dog walking job:
- **Operative receives**: £100.00 (their hourly rate)
- **Client pays total**: £130.20
- **Surely revenue**: £25.20 (£21.00 fee + £4.20 VAT)
- **Emergency premium**: £5.00 (additional for urgent job)

## 🔧 Technical Implementation

### Database Structure
- **Jobs Table**: Stores job details and requirements
- **Contracts Table**: Stores calculated fees and payment status
- **Invoices Table**: Stores payment records and Stripe data

### Calculation Engine
- Fees calculated automatically when contract is created
- Uses standardized rates from Contract model constants
- Prevents manual calculation errors

### Payment Processing
- Stripe integration for secure payments
- Webhook handling for payment confirmations
- Automatic status updates upon payment success