[2025-06-04 20:46:39] local.INFO: Displaying invoice #240 of type escrow  
[2025-06-04 20:53:16] local.INFO: Displaying invoice #240 of type escrow  
[2025-06-04 20:59:00] local.INFO: Displaying invoice #240 of type escrow  
[2025-06-04 21:00:50] local.INFO: Displaying invoice #240 of type escrow  
[2025-06-04 21:02:56] local.INFO: Displaying invoice #240 of type escrow  
[2025-06-04 21:03:12] local.INFO: Displaying invoice #240 of type escrow  
[2025-06-04 21:04:40] local.INFO: Displaying invoice #240 of type escrow  
[2025-06-04 21:05:23] local.DEBUG: Message-ID: <1a55957c1eaf873605e48b066933bea6@127.0.0.1>
Date: Wed, 04 Jun 2025 21:05:23 +0000
Subject: URGENT: New Message for Emergency Hire Job
From: Surely <<EMAIL>>
To: <EMAIL>
MIME-Version: 1.0
Content-Type: text/html; charset=utf-8
Content-Transfer-Encoding: quoted-printable

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URGENT: New Message for Emergency Hire Job</title>
</head>
<body>
    <p>Dear Leo Bianchi,</p>

    <p><strong>URGENT:</strong> You have received a new message regarding an <strong>Emergency Hire</strong> job that requires immediate attention.</p>

    <p><strong>Job Details:</strong></p>
    <ul>
        <li><strong>Title:</strong> payment test</li>
        <li><strong>Location:</strong> B1 1DB</li>
    </ul>

    <p><strong>Message from Olivia Brown:</strong></p>
    <p style="background-color: #f8f9fa; padding: 10px; border-left: 4px solid #dc3545;">Escrow payment of £325.5 has been added to the contract.</p>

    <p><a href="http://localhost:5173/chat/280" style="display: inline-block; background-color: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; font-weight: bold;">View Conversation</a></p>

    <p>Please respond promptly as this is an emergency hire job.</p>

    <p>Thank you for using Surely Security!</p>

    <p>Best regards,<br>
    Aliona & Aurela</p>
</body>
</html>
  
[2025-06-04 21:05:23] local.INFO: Sent emergency hire message <NAME_EMAIL> for job #309  
[2025-06-04 21:05:24] local.INFO: Broadcasting [App\Events\NewMessageEvent] on channels [surely-development] with payload:
{
    "message": {
        "id": 3250,
        "chat_id": 280,
        "sender_id": 822,
        "receiver_id": 823,
        "job_id": null,
        "contract_id": null,
        "payment_id": null,
        "message": "Escrow payment of \u00a3325.5 has been added to the contract.",
        "read": null,
        "type": "payment_status",
        "created_at": "2025-06-04T21:05:23.000000Z",
        "updated_at": "2025-06-04T21:05:23.000000Z",
        "deleted_at": null,
        "receiver": {
            "id": 823,
            "name": "Leo Bianchi",
            "email": "<EMAIL>",
            "phone": "************",
            "status": 1,
            "login_type": "1",
            "platform": "3",
            "social_id": null,
            "account_type": "1",
            "firebase_token": "test",
            "user_update_id": null,
            "created_at": "2024-07-17T16:59:39.000000Z",
            "updated_at": "2025-04-09T18:14:04.000000Z",
            "deleted_at": null,
            "age": null,
            "height": null,
            "gender": null,
            "pay_rate": null,
            "years_of_experience": null,
            "company_registration": null,
            "expertise_id": null,
            "address_1": "London",
            "address_2": null,
            "postal_code": "NW5 3DG",
            "website": null,
            "app_version": "web 1.0",
            "lat": "51.547335",
            "lng": "-0.142343",
            "address": "123 high street",
            "city_id": null,
            "profile_photo": null,
            "profile_video": null,
            "additional_pictures": null,
            "profile_title": "My bio",
            "profile_description": "Bio here",
            "instant_book": 0,
            "account_name": null,
            "sort_code": "108800",
            "account_number": "********",
            "utr_number": null,
            "vat_number": null,
            "company_number": null,
            "location_range": 50,
            "sia_licence_number": null,
            "sia_licence_expiry_date": null,
            "sia_licence_card_photo": null,
            "document_type": null,
            "id_front_document": null,
            "id_back_document": null,
            "address_verification_document": null,
            "selfie_verification_document": null,
            "client_represent_business_as": null,
            "company_registered_number": null,
            "sia_licence_types": [],
            "industry_sectors": [
                "Bars, Clubs & Restaurants"
            ],
            "address_3": null,
            "city": "Camden, London, England",
            "attempts": 0,
            "email_code": null,
            "email_code_expires_at": null,
            "sent_at": null,
            "code": null,
            "email_token": null,
            "email_verified": 1,
            "banned": 0,
            "banned_timestamp": null,
            "operator_vat": null,
            "referal_code": "fe5671",
            "referal_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzM4NCJ9.***********************************************************************************************************************************************************.0M2zBdI0IJI7xciqxd6UbP80rKDu-xFCm0C_ZfL_hyRSVjxJidVWrWVZ2OJAv6TV",
            "emergency_hire_notification_status": 0,
            "email_verification_deadline": null,
            "is_guest": false
        },
        "sender": {
            "id": 822,
            "name": "Olivia Brown",
            "email": "<EMAIL>",
            "phone": "************",
            "status": 1,
            "login_type": "1",
            "platform": "3",
            "social_id": null,
            "account_type": "2",
            "firebase_token": "test",
            "user_update_id": null,
            "created_at": "2024-07-17T16:42:59.000000Z",
            "updated_at": "2024-07-17T16:44:26.000000Z",
            "deleted_at": null,
            "age": null,
            "height": null,
            "gender": null,
            "pay_rate": null,
            "years_of_experience": null,
            "company_registration": null,
            "expertise_id": null,
            "address_1": null,
            "address_2": "Kensington and Chelsea, London, England",
            "postal_code": "SW35qp",
            "website": null,
            "app_version": "web 1.0",
            "lat": "51.485292",
            "lng": "-0.165607",
            "address": null,
            "city_id": null,
            "profile_photo": null,
            "profile_video": null,
            "additional_pictures": null,
            "profile_title": null,
            "profile_description": null,
            "instant_book": 0,
            "account_name": null,
            "sort_code": null,
            "account_number": null,
            "utr_number": null,
            "vat_number": null,
            "company_number": null,
            "location_range": null,
            "sia_licence_number": null,
            "sia_licence_expiry_date": null,
            "sia_licence_card_photo": null,
            "document_type": null,
            "id_front_document": null,
            "id_back_document": null,
            "address_verification_document": null,
            "selfie_verification_document": null,
            "client_represent_business_as": "Private Individual",
            "company_registered_number": null,
            "sia_licence_types": null,
            "industry_sectors": [],
            "address_3": null,
            "city": "Kensington and Chelsea, London, England",
            "attempts": 0,
            "email_code": null,
            "email_code_expires_at": null,
            "sent_at": null,
            "code": null,
            "email_token": null,
            "email_verified": 1,
            "banned": 0,
            "banned_timestamp": null,
            "operator_vat": null,
            "referal_code": "68bef7",
            "referal_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzM4NCJ9.****************************************************************************************************************************************************************.GymkqHyBVpHQd59AfaDBiK06s9ra7q8PWINxj1JwnWW73c7lRq5PPKAx9rwSMvzh",
            "emergency_hire_notification_status": 0,
            "email_verification_deadline": null,
            "is_guest": false
        }
    },
    "socket": null
}  
[2025-06-04 21:07:29] local.INFO: Displaying invoice #240 of type escrow  
[2025-06-04 21:07:47] local.ERROR: ErrorException: Trying to access array offset on null in C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\app\Http\Controllers\Datatables\MobileUserDatatable.php:47
Stack trace:
#0 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\app\Http\Controllers\Datatables\MobileUserDatatable.php(47): Illuminate\Foundation\Bootstrap\HandleExceptions->handleError(2, 'Trying to acces...', 'C:\\Users\\<USER>\\...', 47)
#1 [internal function]: App\Http\Controllers\Datatables\MobileUserDatatable->App\Http\Controllers\Datatables\{closure}(Object(Illuminate\Database\Eloquent\Builder))
#2 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\yajra\laravel-datatables-oracle\src\DataTableAbstract.php(700): call_user_func(Object(Closure), Object(Illuminate\Database\Eloquent\Builder))
#3 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\yajra\laravel-datatables-oracle\src\QueryDataTable.php(142): Yajra\DataTables\DataTableAbstract->filterRecords()
#4 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\yajra\laravel-datatables-oracle\src\QueryDataTable.php(99): Yajra\DataTables\QueryDataTable->prepareQuery()
#5 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\app\Http\Controllers\Datatables\MobileUserDatatable.php(56): Yajra\DataTables\QueryDataTable->make(true)
#6 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Controller.php(54): App\Http\Controllers\Datatables\MobileUserDatatable->mobileUsersData(Object(Illuminate\Http\Request))
#7 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php(45): Illuminate\Routing\Controller->callAction('mobileUsersData', Array)
#8 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Route.php(261): Illuminate\Routing\ControllerDispatcher->dispatch(Object(Illuminate\Routing\Route), Object(App\Http\Controllers\Datatables\MobileUserDatatable), 'mobileUsersData')
#9 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Route.php(205): Illuminate\Routing\Route->runController()
#10 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Router.php(721): Illuminate\Routing\Route->run()
#11 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(128): Illuminate\Routing\Router->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#12 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\app\Http\Middleware\AdminMiddleware.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#13 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\AdminMiddleware->handle(Object(Illuminate\Http\Request), Object(Closure))
#14 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(50): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#15 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Routing\Middleware\SubstituteBindings->handle(Object(Illuminate\Http\Request), Object(Closure))
#16 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Auth\Middleware\Authenticate.php(44): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#17 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Auth\Middleware\Authenticate->handle(Object(Illuminate\Http\Request), Object(Closure))
#18 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php(78): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#19 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\VerifyCsrfToken->handle(Object(Illuminate\Http\Request), Object(Closure))
#20 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\View\Middleware\ShareErrorsFromSession.php(49): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#21 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\View\Middleware\ShareErrorsFromSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#22 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(121): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#23 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(64): Illuminate\Session\Middleware\StartSession->handleStatefulRequest(Object(Illuminate\Http\Request), Object(Illuminate\Session\Store), Object(Closure))
#24 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Session\Middleware\StartSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#25 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse.php(37): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#26 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse->handle(Object(Illuminate\Http\Request), Object(Closure))
#27 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\EncryptCookies.php(67): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#28 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Cookie\Middleware\EncryptCookies->handle(Object(Illuminate\Http\Request), Object(Closure))
#29 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(103): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#30 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Router.php(719): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#31 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Router.php(698): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request))
#32 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Router.php(662): Illuminate\Routing\Router->runRoute(Object(Illuminate\Http\Request), Object(Illuminate\Routing\Route))
#33 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Router.php(651): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#34 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(167): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#35 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(128): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#36 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\livewire\livewire\src\DisableBrowserCache.php(19): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#37 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Livewire\DisableBrowserCache->handle(Object(Illuminate\Http\Request), Object(Closure))
#38 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#39 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#40 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#41 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#42 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#43 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#44 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#45 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#46 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(86): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#47 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#48 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\barryvdh\laravel-cors\src\HandleCors.php(52): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#49 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Fruitcake\Cors\HandleCors->handle(Object(Illuminate\Http\Request), Object(Closure))
#50 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Http\Middleware\TrustProxies.php(39): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#51 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Http\Middleware\TrustProxies->handle(Object(Illuminate\Http\Request), Object(Closure))
#52 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(103): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#53 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(142): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#54 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(111): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#55 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#56 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\server.php(21): require_once('C:\\Users\\<USER>\\...')
#57 {main}  
[2025-06-04 21:07:47] local.INFO: Total invoices found: 240  
[2025-06-04 21:07:47] local.INFO: Formatted data count: 240  
[2025-06-04 21:07:51] local.INFO: Displaying invoice #241 of type escrow  
[2025-06-04 21:11:08] local.INFO: Displaying invoice #241 of type escrow  
[2025-06-04 21:14:21] local.INFO: Displaying invoice #241 of type escrow  
[2025-06-04 21:18:55] local.INFO: Displaying invoice #241 of type escrow  
[2025-06-04 21:20:12] local.DEBUG: Message-ID: <ce234c4406fa762f357846b19cc1f590@127.0.0.1>
Date: Wed, 04 Jun 2025 21:20:12 +0000
Subject: URGENT: New Message for Emergency Hire Job
From: Surely <<EMAIL>>
To: <EMAIL>
MIME-Version: 1.0
Content-Type: text/html; charset=utf-8
Content-Transfer-Encoding: quoted-printable

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URGENT: New Message for Emergency Hire Job</title>
</head>
<body>
    <p>Dear Leo Bianchi,</p>

    <p><strong>URGENT:</strong> You have received a new message regarding an <strong>Emergency Hire</strong> job that requires immediate attention.</p>

    <p><strong>Job Details:</strong></p>
    <ul>
        <li><strong>Title:</strong> payment test</li>
        <li><strong>Location:</strong> B1 1DB</li>
    </ul>

    <p><strong>Message from Olivia Brown:</strong></p>
    <p style="background-color: #f8f9fa; padding: 10px; border-left: 4px solid #dc3545;">Escrow payment of £325.5 has been added to the contract.</p>

    <p><a href="http://localhost:5173/chat/280" style="display: inline-block; background-color: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; font-weight: bold;">View Conversation</a></p>

    <p>Please respond promptly as this is an emergency hire job.</p>

    <p>Thank you for using Surely Security!</p>

    <p>Best regards,<br>
    Aliona & Aurela</p>
</body>
</html>
  
[2025-06-04 21:20:12] local.INFO: Sent emergency hire message <NAME_EMAIL> for job #309  
[2025-06-04 21:20:12] local.INFO: Broadcasting [App\Events\NewMessageEvent] on channels [surely-development] with payload:
{
    "message": {
        "id": 3251,
        "chat_id": 280,
        "sender_id": 822,
        "receiver_id": 823,
        "job_id": null,
        "contract_id": null,
        "payment_id": null,
        "message": "Escrow payment of \u00a3325.5 has been added to the contract.",
        "read": null,
        "type": "payment_status",
        "created_at": "2025-06-04T21:20:12.000000Z",
        "updated_at": "2025-06-04T21:20:12.000000Z",
        "deleted_at": null,
        "receiver": {
            "id": 823,
            "name": "Leo Bianchi",
            "email": "<EMAIL>",
            "phone": "************",
            "status": 1,
            "login_type": "1",
            "platform": "3",
            "social_id": null,
            "account_type": "1",
            "firebase_token": "test",
            "user_update_id": null,
            "created_at": "2024-07-17T16:59:39.000000Z",
            "updated_at": "2025-04-09T18:14:04.000000Z",
            "deleted_at": null,
            "age": null,
            "height": null,
            "gender": null,
            "pay_rate": null,
            "years_of_experience": null,
            "company_registration": null,
            "expertise_id": null,
            "address_1": "London",
            "address_2": null,
            "postal_code": "NW5 3DG",
            "website": null,
            "app_version": "web 1.0",
            "lat": "51.547335",
            "lng": "-0.142343",
            "address": "123 high street",
            "city_id": null,
            "profile_photo": null,
            "profile_video": null,
            "additional_pictures": null,
            "profile_title": "My bio",
            "profile_description": "Bio here",
            "instant_book": 0,
            "account_name": null,
            "sort_code": "108800",
            "account_number": "********",
            "utr_number": null,
            "vat_number": null,
            "company_number": null,
            "location_range": 50,
            "sia_licence_number": null,
            "sia_licence_expiry_date": null,
            "sia_licence_card_photo": null,
            "document_type": null,
            "id_front_document": null,
            "id_back_document": null,
            "address_verification_document": null,
            "selfie_verification_document": null,
            "client_represent_business_as": null,
            "company_registered_number": null,
            "sia_licence_types": [],
            "industry_sectors": [
                "Bars, Clubs & Restaurants"
            ],
            "address_3": null,
            "city": "Camden, London, England",
            "attempts": 0,
            "email_code": null,
            "email_code_expires_at": null,
            "sent_at": null,
            "code": null,
            "email_token": null,
            "email_verified": 1,
            "banned": 0,
            "banned_timestamp": null,
            "operator_vat": null,
            "referal_code": "fe5671",
            "referal_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzM4NCJ9.***********************************************************************************************************************************************************.0M2zBdI0IJI7xciqxd6UbP80rKDu-xFCm0C_ZfL_hyRSVjxJidVWrWVZ2OJAv6TV",
            "emergency_hire_notification_status": 0,
            "email_verification_deadline": null,
            "is_guest": false
        },
        "sender": {
            "id": 822,
            "name": "Olivia Brown",
            "email": "<EMAIL>",
            "phone": "************",
            "status": 1,
            "login_type": "1",
            "platform": "3",
            "social_id": null,
            "account_type": "2",
            "firebase_token": "test",
            "user_update_id": null,
            "created_at": "2024-07-17T16:42:59.000000Z",
            "updated_at": "2024-07-17T16:44:26.000000Z",
            "deleted_at": null,
            "age": null,
            "height": null,
            "gender": null,
            "pay_rate": null,
            "years_of_experience": null,
            "company_registration": null,
            "expertise_id": null,
            "address_1": null,
            "address_2": "Kensington and Chelsea, London, England",
            "postal_code": "SW35qp",
            "website": null,
            "app_version": "web 1.0",
            "lat": "51.485292",
            "lng": "-0.165607",
            "address": null,
            "city_id": null,
            "profile_photo": null,
            "profile_video": null,
            "additional_pictures": null,
            "profile_title": null,
            "profile_description": null,
            "instant_book": 0,
            "account_name": null,
            "sort_code": null,
            "account_number": null,
            "utr_number": null,
            "vat_number": null,
            "company_number": null,
            "location_range": null,
            "sia_licence_number": null,
            "sia_licence_expiry_date": null,
            "sia_licence_card_photo": null,
            "document_type": null,
            "id_front_document": null,
            "id_back_document": null,
            "address_verification_document": null,
            "selfie_verification_document": null,
            "client_represent_business_as": "Private Individual",
            "company_registered_number": null,
            "sia_licence_types": null,
            "industry_sectors": [],
            "address_3": null,
            "city": "Kensington and Chelsea, London, England",
            "attempts": 0,
            "email_code": null,
            "email_code_expires_at": null,
            "sent_at": null,
            "code": null,
            "email_token": null,
            "email_verified": 1,
            "banned": 0,
            "banned_timestamp": null,
            "operator_vat": null,
            "referal_code": "68bef7",
            "referal_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzM4NCJ9.****************************************************************************************************************************************************************.GymkqHyBVpHQd59AfaDBiK06s9ra7q8PWINxj1JwnWW73c7lRq5PPKAx9rwSMvzh",
            "emergency_hire_notification_status": 0,
            "email_verification_deadline": null,
            "is_guest": false
        }
    },
    "socket": null
}  
[2025-06-04 21:20:58] local.ERROR: ErrorException: Trying to access array offset on null in C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\app\Http\Controllers\Datatables\MobileUserDatatable.php:47
Stack trace:
#0 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\app\Http\Controllers\Datatables\MobileUserDatatable.php(47): Illuminate\Foundation\Bootstrap\HandleExceptions->handleError(2, 'Trying to acces...', 'C:\\Users\\<USER>\\...', 47)
#1 [internal function]: App\Http\Controllers\Datatables\MobileUserDatatable->App\Http\Controllers\Datatables\{closure}(Object(Illuminate\Database\Eloquent\Builder))
#2 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\yajra\laravel-datatables-oracle\src\DataTableAbstract.php(700): call_user_func(Object(Closure), Object(Illuminate\Database\Eloquent\Builder))
#3 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\yajra\laravel-datatables-oracle\src\QueryDataTable.php(142): Yajra\DataTables\DataTableAbstract->filterRecords()
#4 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\yajra\laravel-datatables-oracle\src\QueryDataTable.php(99): Yajra\DataTables\QueryDataTable->prepareQuery()
#5 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\app\Http\Controllers\Datatables\MobileUserDatatable.php(56): Yajra\DataTables\QueryDataTable->make(true)
#6 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Controller.php(54): App\Http\Controllers\Datatables\MobileUserDatatable->mobileUsersData(Object(Illuminate\Http\Request))
#7 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php(45): Illuminate\Routing\Controller->callAction('mobileUsersData', Array)
#8 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Route.php(261): Illuminate\Routing\ControllerDispatcher->dispatch(Object(Illuminate\Routing\Route), Object(App\Http\Controllers\Datatables\MobileUserDatatable), 'mobileUsersData')
#9 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Route.php(205): Illuminate\Routing\Route->runController()
#10 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Router.php(721): Illuminate\Routing\Route->run()
#11 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(128): Illuminate\Routing\Router->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#12 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\app\Http\Middleware\AdminMiddleware.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#13 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\AdminMiddleware->handle(Object(Illuminate\Http\Request), Object(Closure))
#14 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(50): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#15 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Routing\Middleware\SubstituteBindings->handle(Object(Illuminate\Http\Request), Object(Closure))
#16 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Auth\Middleware\Authenticate.php(44): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#17 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Auth\Middleware\Authenticate->handle(Object(Illuminate\Http\Request), Object(Closure))
#18 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php(78): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#19 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\VerifyCsrfToken->handle(Object(Illuminate\Http\Request), Object(Closure))
#20 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\View\Middleware\ShareErrorsFromSession.php(49): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#21 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\View\Middleware\ShareErrorsFromSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#22 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(121): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#23 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(64): Illuminate\Session\Middleware\StartSession->handleStatefulRequest(Object(Illuminate\Http\Request), Object(Illuminate\Session\Store), Object(Closure))
#24 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Session\Middleware\StartSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#25 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse.php(37): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#26 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse->handle(Object(Illuminate\Http\Request), Object(Closure))
#27 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\EncryptCookies.php(67): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#28 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Cookie\Middleware\EncryptCookies->handle(Object(Illuminate\Http\Request), Object(Closure))
#29 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(103): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#30 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Router.php(719): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#31 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Router.php(698): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request))
#32 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Router.php(662): Illuminate\Routing\Router->runRoute(Object(Illuminate\Http\Request), Object(Illuminate\Routing\Route))
#33 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Router.php(651): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#34 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(167): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#35 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(128): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#36 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\livewire\livewire\src\DisableBrowserCache.php(19): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#37 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Livewire\DisableBrowserCache->handle(Object(Illuminate\Http\Request), Object(Closure))
#38 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#39 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#40 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#41 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#42 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#43 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#44 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#45 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#46 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(86): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#47 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#48 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\barryvdh\laravel-cors\src\HandleCors.php(52): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#49 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Fruitcake\Cors\HandleCors->handle(Object(Illuminate\Http\Request), Object(Closure))
#50 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Http\Middleware\TrustProxies.php(39): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#51 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Http\Middleware\TrustProxies->handle(Object(Illuminate\Http\Request), Object(Closure))
#52 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(103): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#53 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(142): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#54 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(111): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#55 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#56 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\server.php(21): require_once('C:\\Users\\<USER>\\...')
#57 {main}  
[2025-06-04 21:20:59] local.INFO: Total invoices found: 242  
[2025-06-04 21:20:59] local.INFO: Formatted data count: 242  
[2025-06-04 21:21:22] local.INFO: Displaying invoice #245 of type escrow  
[2025-06-04 21:24:44] local.INFO: Displaying invoice #245 of type escrow  
[2025-06-04 21:25:24] local.INFO: Displaying invoice #245 of type escrow  
[2025-06-04 21:25:50] local.INFO: Displaying invoice #245 of type escrow  
[2025-06-04 21:27:06] local.INFO: Displaying invoice #245 of type escrow  
[2025-06-04 21:27:31] local.ERROR: ErrorException: Trying to access array offset on null in C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\app\Http\Controllers\Datatables\MobileUserDatatable.php:47
Stack trace:
#0 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\app\Http\Controllers\Datatables\MobileUserDatatable.php(47): Illuminate\Foundation\Bootstrap\HandleExceptions->handleError(2, 'Trying to acces...', 'C:\\Users\\<USER>\\...', 47)
#1 [internal function]: App\Http\Controllers\Datatables\MobileUserDatatable->App\Http\Controllers\Datatables\{closure}(Object(Illuminate\Database\Eloquent\Builder))
#2 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\yajra\laravel-datatables-oracle\src\DataTableAbstract.php(700): call_user_func(Object(Closure), Object(Illuminate\Database\Eloquent\Builder))
#3 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\yajra\laravel-datatables-oracle\src\QueryDataTable.php(142): Yajra\DataTables\DataTableAbstract->filterRecords()
#4 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\yajra\laravel-datatables-oracle\src\QueryDataTable.php(99): Yajra\DataTables\QueryDataTable->prepareQuery()
#5 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\app\Http\Controllers\Datatables\MobileUserDatatable.php(56): Yajra\DataTables\QueryDataTable->make(true)
#6 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Controller.php(54): App\Http\Controllers\Datatables\MobileUserDatatable->mobileUsersData(Object(Illuminate\Http\Request))
#7 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php(45): Illuminate\Routing\Controller->callAction('mobileUsersData', Array)
#8 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Route.php(261): Illuminate\Routing\ControllerDispatcher->dispatch(Object(Illuminate\Routing\Route), Object(App\Http\Controllers\Datatables\MobileUserDatatable), 'mobileUsersData')
#9 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Route.php(205): Illuminate\Routing\Route->runController()
#10 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Router.php(721): Illuminate\Routing\Route->run()
#11 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(128): Illuminate\Routing\Router->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#12 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\app\Http\Middleware\AdminMiddleware.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#13 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\AdminMiddleware->handle(Object(Illuminate\Http\Request), Object(Closure))
#14 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(50): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#15 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Routing\Middleware\SubstituteBindings->handle(Object(Illuminate\Http\Request), Object(Closure))
#16 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Auth\Middleware\Authenticate.php(44): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#17 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Auth\Middleware\Authenticate->handle(Object(Illuminate\Http\Request), Object(Closure))
#18 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php(78): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#19 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\VerifyCsrfToken->handle(Object(Illuminate\Http\Request), Object(Closure))
#20 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\View\Middleware\ShareErrorsFromSession.php(49): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#21 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\View\Middleware\ShareErrorsFromSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#22 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(121): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#23 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(64): Illuminate\Session\Middleware\StartSession->handleStatefulRequest(Object(Illuminate\Http\Request), Object(Illuminate\Session\Store), Object(Closure))
#24 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Session\Middleware\StartSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#25 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse.php(37): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#26 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse->handle(Object(Illuminate\Http\Request), Object(Closure))
#27 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\EncryptCookies.php(67): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#28 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Cookie\Middleware\EncryptCookies->handle(Object(Illuminate\Http\Request), Object(Closure))
#29 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(103): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#30 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Router.php(719): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#31 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Router.php(698): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request))
#32 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Router.php(662): Illuminate\Routing\Router->runRoute(Object(Illuminate\Http\Request), Object(Illuminate\Routing\Route))
#33 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Router.php(651): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#34 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(167): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#35 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(128): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#36 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\livewire\livewire\src\DisableBrowserCache.php(19): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#37 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Livewire\DisableBrowserCache->handle(Object(Illuminate\Http\Request), Object(Closure))
#38 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#39 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#40 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#41 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#42 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#43 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#44 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#45 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#46 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(86): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#47 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#48 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\barryvdh\laravel-cors\src\HandleCors.php(52): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#49 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Fruitcake\Cors\HandleCors->handle(Object(Illuminate\Http\Request), Object(Closure))
#50 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Http\Middleware\TrustProxies.php(39): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#51 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Http\Middleware\TrustProxies->handle(Object(Illuminate\Http\Request), Object(Closure))
#52 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(103): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#53 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(142): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#54 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(111): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#55 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#56 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\server.php(21): require_once('C:\\Users\\<USER>\\...')
#57 {main}  
[2025-06-04 21:27:31] local.INFO: Total invoices found: 242  
[2025-06-04 21:27:31] local.INFO: Formatted data count: 242  
[2025-06-04 21:27:34] local.INFO: Displaying invoice #242 of type escrow  
[2025-06-04 21:27:35] local.ERROR: Attempt to read property "name" on null (View: C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\resources\views\invoices\show.blade.php) {"view":{"view":"C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\resources\\views/invoices/show.blade.php","data":{"errors":"<pre class=sf-dump id=sf-dump-1657763016 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Support\\ViewErrorBag</span> {<a class=sf-dump-ref>#1802</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []
</samp>}
</pre><script>Sfdump(\"sf-dump-1657763016\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","invoice":"<pre class=sf-dump id=sf-dump-1940779865 data-indent-pad=\"  \"><span class=sf-dump-note>App\\Models\\Invoice</span> {<a class=sf-dump-ref>#1833</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"8 characters\">invoices</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:25</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>242</span>
    \"<span class=sf-dump-key>amount</span>\" => \"<span class=sf-dump-str title=\"6 characters\">138.60</span>\"
    \"<span class=sf-dump-key>currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GBP</span>\"
    \"<span class=sf-dump-key>description</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>stripe_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>stripe_customer_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>stripe_payment_intent_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>stripe_payment_intent_client_secret</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>stripe_payment_intent_status</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>stripe_payment_invoice_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">escrow</span>\"
    \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">completed</span>\"
    \"<span class=sf-dump-key>payment_status</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>contract_id</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>operator_id</span>\" => <span class=sf-dump-num>9</span>
    \"<span class=sf-dump-key>client_id</span>\" => <span class=sf-dump-num>11</span>
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-04 21:12:20</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-04 21:13:13</span>\"
    \"<span class=sf-dump-key>application_fee_amount</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>application_fee_rate</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>operator_vat_amount</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>operator_vat_rate</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>application_vat_amount</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>application_vat_rate</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>sub_amount</span>\" => <span class=sf-dump-const>null</span>
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:25</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>242</span>
    \"<span class=sf-dump-key>amount</span>\" => \"<span class=sf-dump-str title=\"6 characters\">138.60</span>\"
    \"<span class=sf-dump-key>currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GBP</span>\"
    \"<span class=sf-dump-key>description</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>stripe_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>stripe_customer_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>stripe_payment_intent_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>stripe_payment_intent_client_secret</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>stripe_payment_intent_status</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>stripe_payment_invoice_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">escrow</span>\"
    \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">completed</span>\"
    \"<span class=sf-dump-key>payment_status</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>contract_id</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>operator_id</span>\" => <span class=sf-dump-num>9</span>
    \"<span class=sf-dump-key>client_id</span>\" => <span class=sf-dump-num>11</span>
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-04 21:12:20</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-04 21:13:13</span>\"
    \"<span class=sf-dump-key>application_fee_amount</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>application_fee_rate</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>operator_vat_amount</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>operator_vat_rate</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>application_vat_amount</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>application_vat_rate</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>sub_amount</span>\" => <span class=sf-dump-const>null</span>
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dates</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:23</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">contract_id</span>\"
    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"6 characters\">job_id</span>\"
    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"9 characters\">client_id</span>\"
    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"11 characters\">operator_id</span>\"
    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"6 characters\">amount</span>\"
    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"8 characters\">currency</span>\"
    <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"11 characters\">description</span>\"
    <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"9 characters\">stripe_id</span>\"
    <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"18 characters\">stripe_customer_id</span>\"
    <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"24 characters\">stripe_payment_intent_id</span>\"
    <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"35 characters\">stripe_payment_intent_client_secret</span>\"
    <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"28 characters\">stripe_payment_intent_status</span>\"
    <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"25 characters\">stripe_payment_invoice_id</span>\"
    <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"4 characters\">type</span>\"
    <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"
    <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"10 characters\">sub_amount</span>\"
    <span class=sf-dump-index>16</span> => \"<span class=sf-dump-str title=\"14 characters\">payment_status</span>\"
    <span class=sf-dump-index>17</span> => \"<span class=sf-dump-str title=\"20 characters\">application_vat_rate</span>\"
    <span class=sf-dump-index>18</span> => \"<span class=sf-dump-str title=\"22 characters\">application_vat_amount</span>\"
    <span class=sf-dump-index>19</span> => \"<span class=sf-dump-str title=\"17 characters\">operator_vat_rate</span>\"
    <span class=sf-dump-index>20</span> => \"<span class=sf-dump-str title=\"19 characters\">operator_vat_amount</span>\"
    <span class=sf-dump-index>21</span> => \"<span class=sf-dump-str title=\"20 characters\">application_fee_rate</span>\"
    <span class=sf-dump-index>22</span> => \"<span class=sf-dump-str title=\"22 characters\">application_fee_amount</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"
  </samp>]
</samp>}
</pre><script>Sfdump(\"sf-dump-1940779865\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","contract":"<pre class=sf-dump id=sf-dump-756776386 data-indent-pad=\"  \"><span class=sf-dump-note>App\\Models\\Contract</span> {<a class=sf-dump-ref>#1835</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"9 characters\">contracts</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:34</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>job_id</span>\" => <span class=sf-dump-num>2</span>
    \"<span class=sf-dump-key>client_id</span>\" => <span class=sf-dump-num>11</span>
    \"<span class=sf-dump-key>operative_id</span>\" => <span class=sf-dump-num>9</span>
    \"<span class=sf-dump-key>chat_id</span>\" => <span class=sf-dump-num>2</span>
    \"<span class=sf-dump-key>hourly_rate</span>\" => \"<span class=sf-dump-str title=\"5 characters\">11.00</span>\"
    \"<span class=sf-dump-key>location</span>\" => \"<span class=sf-dump-str title=\"6 characters\">B1 1FU</span>\"
    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">job_invite</span>\"
    \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str>4</span>\"
    \"<span class=sf-dump-key>start_date</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2024-04-12 12:27:27</span>\"
    \"<span class=sf-dump-key>end_date</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>date_range</span>\" => \"<span class=sf-dump-str title=\"888 characters\">[{&quot;end&quot;: &quot;2024-04-17T04:02:00.000Z&quot;, &quot;start&quot;: &quot;2024-04-17T03:01:00.000Z&quot;}, {&quot;end&quot;: &quot;2024-04-18T04:02:00.000Z&quot;, &quot;start&quot;: &quot;2024-04-18T03:01:00.000Z&quot;}, {&quot;end&quot;: &quot;2024-04-19T04:02:00.000Z&quot;, &quot;start&quot;: &quot;2024-04-19T03:01:00.000Z&quot;}, {&quot;end&quot;: &quot;2024-04-20T04:02:00.000Z&quot;, &quot;start&quot;: &quot;2024-04-20T03:01:00.000Z&quot;}, {&quot;end&quot;: &quot;2024-04-21T04:02:00.000Z&quot;, &quot;start&quot;: &quot;2024-04-21T03:01:00.000Z&quot;}, {&quot;end&quot;: &quot;2024-04-22T04:02:00.000Z&quot;, &quot;start&quot;: &quot;2024-04-22T03:01:00.000Z&quot;}, {&quot;end&quot;: &quot;2024-04-23T04:02:00.000Z&quot;, &quot;start&quot;: &quot;2024-04-23T03:01:00.000Z&quot;}, {&quot;end&quot;: &quot;2024-04-24T04:02:00.000Z&quot;, &quot;start&quot;: &quot;2024-04-24T03:01:00.000Z&quot;}, {&quot;end&quot;: &quot;2024-04-25T04:02:00.000Z&quot;, &quot;start&quot;: &quot;2024-04-25T03:01:00.000Z&quot;}, {&quot;end&quot;: &quot;2024-04-26T04:02:00.000Z&quot;, &quot;start&quot;: &quot;2024-04-26T03:01:00.000Z&quot;}, {&quot;end&quot;: &quot;2024-05-02T02:02:00.000Z&quot;, &quot;start&quot;: &quot;2024-05-02T01:01:00.000Z&quot;}, {&quot;end&quot;: &quot;2024-05-04T02:02:00.000Z&quot;, &quot;start&quot;: &quot;2024-05-04T01:01:00.000Z&quot;}]</span>\"
    \"<span class=sf-dump-key>shifts_status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">confirmed</span>\"
    \"<span class=sf-dump-key>shifts_accepted_at</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>payment_terms</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2024-04-12 12:27:27</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-04 21:13:13</span>\"
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"4 characters\">paid</span>\"
    \"<span class=sf-dump-key>escrow_status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">pending</span>\"
    \"<span class=sf-dump-key>escrow_amount</span>\" => \"<span class=sf-dump-str title=\"5 characters\">27.72</span>\"
    \"<span class=sf-dump-key>total_amount</span>\" => \"<span class=sf-dump-str title=\"5 characters\">138.6</span>\"
    \"<span class=sf-dump-key>reason</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>application_vat_rate</span>\" => \"<span class=sf-dump-str>0</span>\"
    \"<span class=sf-dump-key>application_vat_amount</span>\" => \"<span class=sf-dump-str>0</span>\"
    \"<span class=sf-dump-key>sub_total</span>\" => \"<span class=sf-dump-str title=\"3 characters\">132</span>\"
    \"<span class=sf-dump-key>application_fee_amount</span>\" => \"<span class=sf-dump-str>0</span>\"
    \"<span class=sf-dump-key>application_fee_rate</span>\" => \"<span class=sf-dump-str>0</span>\"
    \"<span class=sf-dump-key>payment_transaction_fee_rate</span>\" => \"<span class=sf-dump-str>5</span>\"
    \"<span class=sf-dump-key>payment_transaction_fee_amount</span>\" => \"<span class=sf-dump-str title=\"3 characters\">6.6</span>\"
    \"<span class=sf-dump-key>operator_vat_rate</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>operator_vat_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>escrow_rate</span>\" => \"<span class=sf-dump-str title=\"2 characters\">20</span>\"
    \"<span class=sf-dump-key>emergency_hire_fee_rate</span>\" => \"<span class=sf-dump-str>0</span>\"
    \"<span class=sf-dump-key>emergency_hire_fee_amount</span>\" => \"<span class=sf-dump-str>0</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:34</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>job_id</span>\" => <span class=sf-dump-num>2</span>
    \"<span class=sf-dump-key>client_id</span>\" => <span class=sf-dump-num>11</span>
    \"<span class=sf-dump-key>operative_id</span>\" => <span class=sf-dump-num>9</span>
    \"<span class=sf-dump-key>chat_id</span>\" => <span class=sf-dump-num>2</span>
    \"<span class=sf-dump-key>hourly_rate</span>\" => \"<span class=sf-dump-str title=\"5 characters\">11.00</span>\"
    \"<span class=sf-dump-key>location</span>\" => \"<span class=sf-dump-str title=\"6 characters\">B1 1FU</span>\"
    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">job_invite</span>\"
    \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str>4</span>\"
    \"<span class=sf-dump-key>start_date</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2024-04-12 12:27:27</span>\"
    \"<span class=sf-dump-key>end_date</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>date_range</span>\" => \"<span class=sf-dump-str title=\"888 characters\">[{&quot;end&quot;: &quot;2024-04-17T04:02:00.000Z&quot;, &quot;start&quot;: &quot;2024-04-17T03:01:00.000Z&quot;}, {&quot;end&quot;: &quot;2024-04-18T04:02:00.000Z&quot;, &quot;start&quot;: &quot;2024-04-18T03:01:00.000Z&quot;}, {&quot;end&quot;: &quot;2024-04-19T04:02:00.000Z&quot;, &quot;start&quot;: &quot;2024-04-19T03:01:00.000Z&quot;}, {&quot;end&quot;: &quot;2024-04-20T04:02:00.000Z&quot;, &quot;start&quot;: &quot;2024-04-20T03:01:00.000Z&quot;}, {&quot;end&quot;: &quot;2024-04-21T04:02:00.000Z&quot;, &quot;start&quot;: &quot;2024-04-21T03:01:00.000Z&quot;}, {&quot;end&quot;: &quot;2024-04-22T04:02:00.000Z&quot;, &quot;start&quot;: &quot;2024-04-22T03:01:00.000Z&quot;}, {&quot;end&quot;: &quot;2024-04-23T04:02:00.000Z&quot;, &quot;start&quot;: &quot;2024-04-23T03:01:00.000Z&quot;}, {&quot;end&quot;: &quot;2024-04-24T04:02:00.000Z&quot;, &quot;start&quot;: &quot;2024-04-24T03:01:00.000Z&quot;}, {&quot;end&quot;: &quot;2024-04-25T04:02:00.000Z&quot;, &quot;start&quot;: &quot;2024-04-25T03:01:00.000Z&quot;}, {&quot;end&quot;: &quot;2024-04-26T04:02:00.000Z&quot;, &quot;start&quot;: &quot;2024-04-26T03:01:00.000Z&quot;}, {&quot;end&quot;: &quot;2024-05-02T02:02:00.000Z&quot;, &quot;start&quot;: &quot;2024-05-02T01:01:00.000Z&quot;}, {&quot;end&quot;: &quot;2024-05-04T02:02:00.000Z&quot;, &quot;start&quot;: &quot;2024-05-04T01:01:00.000Z&quot;}]</span>\"
    \"<span class=sf-dump-key>shifts_status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">confirmed</span>\"
    \"<span class=sf-dump-key>shifts_accepted_at</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>payment_terms</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2024-04-12 12:27:27</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-04 21:13:13</span>\"
    \"<span class=sf-dump-key>payment_status</span>\" => \"<span class=sf-dump-str title=\"4 characters\">paid</span>\"
    \"<span class=sf-dump-key>escrow_status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">pending</span>\"
    \"<span class=sf-dump-key>escrow_amount</span>\" => \"<span class=sf-dump-str title=\"5 characters\">27.72</span>\"
    \"<span class=sf-dump-key>total_amount</span>\" => \"<span class=sf-dump-str title=\"5 characters\">138.6</span>\"
    \"<span class=sf-dump-key>reason</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>application_vat_rate</span>\" => \"<span class=sf-dump-str>0</span>\"
    \"<span class=sf-dump-key>application_vat_amount</span>\" => \"<span class=sf-dump-str>0</span>\"
    \"<span class=sf-dump-key>sub_total</span>\" => \"<span class=sf-dump-str title=\"3 characters\">132</span>\"
    \"<span class=sf-dump-key>application_fee_amount</span>\" => \"<span class=sf-dump-str>0</span>\"
    \"<span class=sf-dump-key>application_fee_rate</span>\" => \"<span class=sf-dump-str>0</span>\"
    \"<span class=sf-dump-key>payment_transaction_fee_rate</span>\" => \"<span class=sf-dump-str>5</span>\"
    \"<span class=sf-dump-key>payment_transaction_fee_amount</span>\" => \"<span class=sf-dump-str title=\"3 characters\">6.6</span>\"
    \"<span class=sf-dump-key>operator_vat_rate</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>operator_vat_amount</span>\" => <span class=sf-dump-num>0.0</span>
    \"<span class=sf-dump-key>escrow_rate</span>\" => \"<span class=sf-dump-str title=\"2 characters\">20</span>\"
    \"<span class=sf-dump-key>emergency_hire_fee_rate</span>\" => \"<span class=sf-dump-str>0</span>\"
    \"<span class=sf-dump-key>emergency_hire_fee_amount</span>\" => \"<span class=sf-dump-str>0</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>date_range</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dates</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>client</span>\" => <span class=sf-dump-note title=\"App\\Models\\MobileUser
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span>MobileUser</span> {<a class=sf-dump-ref>#1862</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"12 characters\">mobile_users</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:73</span> [ &#8230;73]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:73</span> [ &#8230;73]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:6</span> [ &#8230;6]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dates</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">rememberTokenName</span>: \"<span class=sf-dump-str title=\"14 characters\">remember_token</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">accessToken</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>
    </samp>}
    \"<span class=sf-dump-key>operative</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>job</span>\" => <span class=sf-dump-const>null</span>
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:31</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">job_id</span>\"
    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"9 characters\">client_id</span>\"
    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"12 characters\">operative_id</span>\"
    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"7 characters\">chat_id</span>\"
    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"11 characters\">hourly_rate</span>\"
    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"8 characters\">location</span>\"
    <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"4 characters\">type</span>\"
    <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"
    <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"10 characters\">start_date</span>\"
    <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"8 characters\">end_date</span>\"
    <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"10 characters\">date_range</span>\"
    <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"13 characters\">shifts_status</span>\"
    <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"13 characters\">payment_terms</span>\"
    <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"13 characters\">escrow_status</span>\"
    <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"13 characters\">escrow_amount</span>\"
    <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"14 characters\">payment_status</span>\"
    <span class=sf-dump-index>16</span> => \"<span class=sf-dump-str title=\"12 characters\">total_amount</span>\"
    <span class=sf-dump-index>17</span> => \"<span class=sf-dump-str title=\"18 characters\">shifts_accepted_at</span>\"
    <span class=sf-dump-index>18</span> => \"<span class=sf-dump-str title=\"6 characters\">reason</span>\"
    <span class=sf-dump-index>19</span> => \"<span class=sf-dump-str title=\"22 characters\">application_vat_amount</span>\"
    <span class=sf-dump-index>20</span> => \"<span class=sf-dump-str title=\"20 characters\">application_vat_rate</span>\"
    <span class=sf-dump-index>21</span> => \"<span class=sf-dump-str title=\"9 characters\">sub_total</span>\"
    <span class=sf-dump-index>22</span> => \"<span class=sf-dump-str title=\"22 characters\">application_fee_amount</span>\"
    <span class=sf-dump-index>23</span> => \"<span class=sf-dump-str title=\"20 characters\">application_fee_rate</span>\"
    <span class=sf-dump-index>24</span> => \"<span class=sf-dump-str title=\"19 characters\">operator_vat_amount</span>\"
    <span class=sf-dump-index>25</span> => \"<span class=sf-dump-str title=\"17 characters\">operator_vat_rate</span>\"
    <span class=sf-dump-index>26</span> => \"<span class=sf-dump-str title=\"11 characters\">escrow_rate</span>\"
    <span class=sf-dump-index>27</span> => \"<span class=sf-dump-str title=\"23 characters\">emergency_hire_fee_rate</span>\"
    <span class=sf-dump-index>28</span> => \"<span class=sf-dump-str title=\"25 characters\">emergency_hire_fee_amount</span>\"
    <span class=sf-dump-index>29</span> => \"<span class=sf-dump-str title=\"30 characters\">payment_transaction_fee_amount</span>\"
    <span class=sf-dump-index>30</span> => \"<span class=sf-dump-str title=\"28 characters\">payment_transaction_fee_rate</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"
  </samp>]
</samp>}
</pre><script>Sfdump(\"sf-dump-756776386\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","invoiceEscrow":"<pre class=sf-dump id=sf-dump-1107716261 data-indent-pad=\"  \"><span class=sf-dump-note>App\\Models\\Invoice</span> {<a class=sf-dump-ref>#1833</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"8 characters\">invoices</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:25</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>242</span>
    \"<span class=sf-dump-key>amount</span>\" => \"<span class=sf-dump-str title=\"6 characters\">138.60</span>\"
    \"<span class=sf-dump-key>currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GBP</span>\"
    \"<span class=sf-dump-key>description</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>stripe_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>stripe_customer_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>stripe_payment_intent_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>stripe_payment_intent_client_secret</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>stripe_payment_intent_status</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>stripe_payment_invoice_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">escrow</span>\"
    \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">completed</span>\"
    \"<span class=sf-dump-key>payment_status</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>contract_id</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>operator_id</span>\" => <span class=sf-dump-num>9</span>
    \"<span class=sf-dump-key>client_id</span>\" => <span class=sf-dump-num>11</span>
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-04 21:12:20</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-04 21:13:13</span>\"
    \"<span class=sf-dump-key>application_fee_amount</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>application_fee_rate</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>operator_vat_amount</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>operator_vat_rate</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>application_vat_amount</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>application_vat_rate</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>sub_amount</span>\" => <span class=sf-dump-const>null</span>
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:25</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>242</span>
    \"<span class=sf-dump-key>amount</span>\" => \"<span class=sf-dump-str title=\"6 characters\">138.60</span>\"
    \"<span class=sf-dump-key>currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GBP</span>\"
    \"<span class=sf-dump-key>description</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>stripe_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>stripe_customer_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>stripe_payment_intent_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>stripe_payment_intent_client_secret</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>stripe_payment_intent_status</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>stripe_payment_invoice_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">escrow</span>\"
    \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"9 characters\">completed</span>\"
    \"<span class=sf-dump-key>payment_status</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>contract_id</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>operator_id</span>\" => <span class=sf-dump-num>9</span>
    \"<span class=sf-dump-key>client_id</span>\" => <span class=sf-dump-num>11</span>
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-04 21:12:20</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-04 21:13:13</span>\"
    \"<span class=sf-dump-key>application_fee_amount</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>application_fee_rate</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>operator_vat_amount</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>operator_vat_rate</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>application_vat_amount</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>application_vat_rate</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>sub_amount</span>\" => <span class=sf-dump-const>null</span>
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dates</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:23</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">contract_id</span>\"
    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"6 characters\">job_id</span>\"
    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"9 characters\">client_id</span>\"
    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"11 characters\">operator_id</span>\"
    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"6 characters\">amount</span>\"
    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"8 characters\">currency</span>\"
    <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"11 characters\">description</span>\"
    <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"9 characters\">stripe_id</span>\"
    <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"18 characters\">stripe_customer_id</span>\"
    <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"24 characters\">stripe_payment_intent_id</span>\"
    <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"35 characters\">stripe_payment_intent_client_secret</span>\"
    <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"28 characters\">stripe_payment_intent_status</span>\"
    <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"25 characters\">stripe_payment_invoice_id</span>\"
    <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"4 characters\">type</span>\"
    <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"
    <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"10 characters\">sub_amount</span>\"
    <span class=sf-dump-index>16</span> => \"<span class=sf-dump-str title=\"14 characters\">payment_status</span>\"
    <span class=sf-dump-index>17</span> => \"<span class=sf-dump-str title=\"20 characters\">application_vat_rate</span>\"
    <span class=sf-dump-index>18</span> => \"<span class=sf-dump-str title=\"22 characters\">application_vat_amount</span>\"
    <span class=sf-dump-index>19</span> => \"<span class=sf-dump-str title=\"17 characters\">operator_vat_rate</span>\"
    <span class=sf-dump-index>20</span> => \"<span class=sf-dump-str title=\"19 characters\">operator_vat_amount</span>\"
    <span class=sf-dump-index>21</span> => \"<span class=sf-dump-str title=\"20 characters\">application_fee_rate</span>\"
    <span class=sf-dump-index>22</span> => \"<span class=sf-dump-str title=\"22 characters\">application_fee_amount</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"
  </samp>]
</samp>}
</pre><script>Sfdump(\"sf-dump-1107716261\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","invoiceOutstanding":"<pre class=sf-dump id=sf-dump-1814617894 data-indent-pad=\"  \"><span class=sf-dump-const>null</span>
</pre><script>Sfdump(\"sf-dump-1814617894\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","dueDate":"<pre class=sf-dump id=sf-dump-1515972712 data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"10 characters\">14-06-2025</span>\"
</pre><script>Sfdump(\"sf-dump-1515972712\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","issueDate":"<pre class=sf-dump id=sf-dump-1541062383 data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"10 characters\">04-06-2025</span>\"
</pre><script>Sfdump(\"sf-dump-1541062383\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","sub_amount":"<pre class=sf-dump id=sf-dump-1095923139 data-indent-pad=\"  \"><span class=sf-dump-num>0</span>
</pre><script>Sfdump(\"sf-dump-1095923139\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","application_fee_amount":"<pre class=sf-dump id=sf-dump-2131660663 data-indent-pad=\"  \"><span class=sf-dump-num>0</span>
</pre><script>Sfdump(\"sf-dump-2131660663\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","application_fee_rate":"<pre class=sf-dump id=sf-dump-2138402631 data-indent-pad=\"  \"><span class=sf-dump-num>0</span>
</pre><script>Sfdump(\"sf-dump-2138402631\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","operator_vat_amount":"<pre class=sf-dump id=sf-dump-567181783 data-indent-pad=\"  \"><span class=sf-dump-num>0</span>
</pre><script>Sfdump(\"sf-dump-567181783\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","operator_vat_rate":"<pre class=sf-dump id=sf-dump-1627976263 data-indent-pad=\"  \"><span class=sf-dump-num>0</span>
</pre><script>Sfdump(\"sf-dump-1627976263\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","application_vat_amount":"<pre class=sf-dump id=sf-dump-769776036 data-indent-pad=\"  \"><span class=sf-dump-num>0</span>
</pre><script>Sfdump(\"sf-dump-769776036\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","application_vat_rate":"<pre class=sf-dump id=sf-dump-691848594 data-indent-pad=\"  \"><span class=sf-dump-num>0</span>
</pre><script>Sfdump(\"sf-dump-691848594\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","payment_status":"<pre class=sf-dump id=sf-dump-2030362115 data-indent-pad=\"  \">\"<span class=sf-dump-str title=\"7 characters\">pending</span>\"
</pre><script>Sfdump(\"sf-dump-2030362115\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
"}},"userId":1,"exception":"[object] (Facade\\Ignition\\Exceptions\\ViewException(code: 0): Attempt to read property \"name\" on null (View: C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\resources\\views\\invoices\\show.blade.php) at C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\resources\\views/invoices/show.blade.php:236)
[stacktrace]
#0 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\resources\\views/invoices/show.blade.php(236): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Attempt to read...', 'C:\\\\Users\\\\<USER>\\\\...', 240)
#1 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(107): require('C:\\\\Users\\\\<USER>\\\\...')
#2 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(108): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#3 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\\\...', Array)
#4 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(69): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\\\...', Array)
#5 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(61): Livewire\\CompilerEngineForIgnition->evaluatePath('C:\\\\Users\\\\<USER>\\\\...', Array)
#6 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\facade\\ignition\\src\\Views\\Engines\\CompilerEngine.php(37): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\\\...', Array)
#7 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(35): Facade\\Ignition\\Views\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\\\...', Array)
#8 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(139): Livewire\\CompilerEngineForIgnition->get('C:\\\\Users\\\\<USER>\\\\...', Array)
#9 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(122): Illuminate\\View\\View->getContents()
#10 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(91): Illuminate\\View\\View->renderContents()
#11 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#12 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#13 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(820): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#14 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(789): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#15 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(720): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#16 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\app\\Http\\Middleware\\AdminMiddleware.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\AdminMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#29 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(719): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#36 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#38 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Livewire\\DisableBrowserCache->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\barryvdh\\laravel-cors\\src\\HandleCors.php(52): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#59 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#60 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#61 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\server.php(21): require_once('C:\\\\Users\\\\<USER>\\\\...')
#62 {main}

[previous exception] [object] (ErrorException(code: 0): Attempt to read property \"name\" on null at C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\storage\\framework\\views\\4526594955e5ef7f2490348927710ee30b3a79a7.php:240)
[stacktrace]
#0 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\storage\\framework\\views\\4526594955e5ef7f2490348927710ee30b3a79a7.php(240): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Attempt to read...', 'C:\\\\Users\\\\<USER>\\\\...', 240)
#1 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(107): require('C:\\\\Users\\\\<USER>\\\\...')
#2 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(108): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#3 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('C:\\\\Users\\\\<USER>\\\\...', Array)
#4 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(69): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('C:\\\\Users\\\\<USER>\\\\...', Array)
#5 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(61): Livewire\\CompilerEngineForIgnition->evaluatePath('C:\\\\Users\\\\<USER>\\\\...', Array)
#6 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\facade\\ignition\\src\\Views\\Engines\\CompilerEngine.php(37): Illuminate\\View\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\\\...', Array)
#7 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php(35): Facade\\Ignition\\Views\\Engines\\CompilerEngine->get('C:\\\\Users\\\\<USER>\\\\...', Array)
#8 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(139): Livewire\\CompilerEngineForIgnition->get('C:\\\\Users\\\\<USER>\\\\...', Array)
#9 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(122): Illuminate\\View\\View->getContents()
#10 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(91): Illuminate\\View\\View->renderContents()
#11 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#12 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#13 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(820): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#14 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(789): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#15 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(720): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#16 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\app\\Http\\Middleware\\AdminMiddleware.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\AdminMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#29 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(719): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#36 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#37 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#38 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Livewire\\DisableBrowserCache->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\barryvdh\\laravel-cors\\src\\HandleCors.php(52): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#59 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#60 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#61 C:\\Users\\<USER>\\GitHub_LocalRespository\\surely-server\\surely_backend\\app-files\\server.php(21): require_once('C:\\\\Users\\\\<USER>\\\\...')
#62 {main}
"} 
[2025-06-04 21:27:42] local.INFO: Displaying invoice #241 of type escrow  
[2025-06-04 21:27:49] local.INFO: Displaying invoice #239 of type escrow  
[2025-06-04 21:28:16] local.INFO: Displaying invoice #224 of type escrow  
[2025-06-04 21:28:25] local.INFO: Displaying invoice #222 of type escrow  
[2025-06-04 21:28:51] local.ERROR: ErrorException: Trying to access array offset on null in C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\app\Http\Controllers\Datatables\MobileUserDatatable.php:47
Stack trace:
#0 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\app\Http\Controllers\Datatables\MobileUserDatatable.php(47): Illuminate\Foundation\Bootstrap\HandleExceptions->handleError(2, 'Trying to acces...', 'C:\\Users\\<USER>\\...', 47)
#1 [internal function]: App\Http\Controllers\Datatables\MobileUserDatatable->App\Http\Controllers\Datatables\{closure}(Object(Illuminate\Database\Eloquent\Builder))
#2 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\yajra\laravel-datatables-oracle\src\DataTableAbstract.php(700): call_user_func(Object(Closure), Object(Illuminate\Database\Eloquent\Builder))
#3 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\yajra\laravel-datatables-oracle\src\QueryDataTable.php(142): Yajra\DataTables\DataTableAbstract->filterRecords()
#4 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\yajra\laravel-datatables-oracle\src\QueryDataTable.php(99): Yajra\DataTables\QueryDataTable->prepareQuery()
#5 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\app\Http\Controllers\Datatables\MobileUserDatatable.php(56): Yajra\DataTables\QueryDataTable->make(true)
#6 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Controller.php(54): App\Http\Controllers\Datatables\MobileUserDatatable->mobileUsersData(Object(Illuminate\Http\Request))
#7 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php(45): Illuminate\Routing\Controller->callAction('mobileUsersData', Array)
#8 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Route.php(261): Illuminate\Routing\ControllerDispatcher->dispatch(Object(Illuminate\Routing\Route), Object(App\Http\Controllers\Datatables\MobileUserDatatable), 'mobileUsersData')
#9 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Route.php(205): Illuminate\Routing\Route->runController()
#10 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Router.php(721): Illuminate\Routing\Route->run()
#11 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(128): Illuminate\Routing\Router->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#12 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\app\Http\Middleware\AdminMiddleware.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#13 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\AdminMiddleware->handle(Object(Illuminate\Http\Request), Object(Closure))
#14 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(50): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#15 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Routing\Middleware\SubstituteBindings->handle(Object(Illuminate\Http\Request), Object(Closure))
#16 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Auth\Middleware\Authenticate.php(44): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#17 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Auth\Middleware\Authenticate->handle(Object(Illuminate\Http\Request), Object(Closure))
#18 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php(78): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#19 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\VerifyCsrfToken->handle(Object(Illuminate\Http\Request), Object(Closure))
#20 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\View\Middleware\ShareErrorsFromSession.php(49): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#21 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\View\Middleware\ShareErrorsFromSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#22 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(121): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#23 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(64): Illuminate\Session\Middleware\StartSession->handleStatefulRequest(Object(Illuminate\Http\Request), Object(Illuminate\Session\Store), Object(Closure))
#24 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Session\Middleware\StartSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#25 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse.php(37): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#26 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse->handle(Object(Illuminate\Http\Request), Object(Closure))
#27 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\EncryptCookies.php(67): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#28 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Cookie\Middleware\EncryptCookies->handle(Object(Illuminate\Http\Request), Object(Closure))
#29 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(103): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#30 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Router.php(719): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#31 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Router.php(698): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request))
#32 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Router.php(662): Illuminate\Routing\Router->runRoute(Object(Illuminate\Http\Request), Object(Illuminate\Routing\Route))
#33 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Router.php(651): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#34 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(167): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#35 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(128): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#36 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\livewire\livewire\src\DisableBrowserCache.php(19): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#37 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Livewire\DisableBrowserCache->handle(Object(Illuminate\Http\Request), Object(Closure))
#38 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#39 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#40 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#41 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#42 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#43 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#44 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#45 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#46 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(86): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#47 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#48 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\barryvdh\laravel-cors\src\HandleCors.php(52): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#49 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Fruitcake\Cors\HandleCors->handle(Object(Illuminate\Http\Request), Object(Closure))
#50 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Http\Middleware\TrustProxies.php(39): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#51 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Http\Middleware\TrustProxies->handle(Object(Illuminate\Http\Request), Object(Closure))
#52 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(103): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#53 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(142): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#54 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(111): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#55 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#56 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\server.php(21): require_once('C:\\Users\\<USER>\\...')
#57 {main}  
[2025-06-04 21:28:51] local.INFO: Total invoices found: 242  
[2025-06-04 21:28:51] local.INFO: Formatted data count: 242  
[2025-06-04 21:28:54] local.INFO: Displaying invoice #245 of type escrow  
[2025-06-04 21:30:22] local.ERROR: ErrorException: Trying to access array offset on null in C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\app\Http\Controllers\Datatables\MobileUserDatatable.php:47
Stack trace:
#0 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\app\Http\Controllers\Datatables\MobileUserDatatable.php(47): Illuminate\Foundation\Bootstrap\HandleExceptions->handleError(2, 'Trying to acces...', 'C:\\Users\\<USER>\\...', 47)
#1 [internal function]: App\Http\Controllers\Datatables\MobileUserDatatable->App\Http\Controllers\Datatables\{closure}(Object(Illuminate\Database\Eloquent\Builder))
#2 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\yajra\laravel-datatables-oracle\src\DataTableAbstract.php(700): call_user_func(Object(Closure), Object(Illuminate\Database\Eloquent\Builder))
#3 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\yajra\laravel-datatables-oracle\src\QueryDataTable.php(142): Yajra\DataTables\DataTableAbstract->filterRecords()
#4 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\yajra\laravel-datatables-oracle\src\QueryDataTable.php(99): Yajra\DataTables\QueryDataTable->prepareQuery()
#5 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\app\Http\Controllers\Datatables\MobileUserDatatable.php(56): Yajra\DataTables\QueryDataTable->make(true)
#6 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Controller.php(54): App\Http\Controllers\Datatables\MobileUserDatatable->mobileUsersData(Object(Illuminate\Http\Request))
#7 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php(45): Illuminate\Routing\Controller->callAction('mobileUsersData', Array)
#8 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Route.php(261): Illuminate\Routing\ControllerDispatcher->dispatch(Object(Illuminate\Routing\Route), Object(App\Http\Controllers\Datatables\MobileUserDatatable), 'mobileUsersData')
#9 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Route.php(205): Illuminate\Routing\Route->runController()
#10 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Router.php(721): Illuminate\Routing\Route->run()
#11 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(128): Illuminate\Routing\Router->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#12 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\app\Http\Middleware\AdminMiddleware.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#13 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\AdminMiddleware->handle(Object(Illuminate\Http\Request), Object(Closure))
#14 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(50): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#15 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Routing\Middleware\SubstituteBindings->handle(Object(Illuminate\Http\Request), Object(Closure))
#16 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Auth\Middleware\Authenticate.php(44): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#17 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Auth\Middleware\Authenticate->handle(Object(Illuminate\Http\Request), Object(Closure))
#18 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php(78): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#19 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\VerifyCsrfToken->handle(Object(Illuminate\Http\Request), Object(Closure))
#20 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\View\Middleware\ShareErrorsFromSession.php(49): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#21 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\View\Middleware\ShareErrorsFromSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#22 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(121): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#23 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(64): Illuminate\Session\Middleware\StartSession->handleStatefulRequest(Object(Illuminate\Http\Request), Object(Illuminate\Session\Store), Object(Closure))
#24 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Session\Middleware\StartSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#25 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse.php(37): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#26 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse->handle(Object(Illuminate\Http\Request), Object(Closure))
#27 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\EncryptCookies.php(67): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#28 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Cookie\Middleware\EncryptCookies->handle(Object(Illuminate\Http\Request), Object(Closure))
#29 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(103): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#30 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Router.php(719): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#31 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Router.php(698): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request))
#32 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Router.php(662): Illuminate\Routing\Router->runRoute(Object(Illuminate\Http\Request), Object(Illuminate\Routing\Route))
#33 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Router.php(651): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#34 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(167): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#35 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(128): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#36 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\livewire\livewire\src\DisableBrowserCache.php(19): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#37 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Livewire\DisableBrowserCache->handle(Object(Illuminate\Http\Request), Object(Closure))
#38 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#39 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#40 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#41 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#42 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#43 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#44 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#45 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#46 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(86): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#47 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#48 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\barryvdh\laravel-cors\src\HandleCors.php(52): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#49 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Fruitcake\Cors\HandleCors->handle(Object(Illuminate\Http\Request), Object(Closure))
#50 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Http\Middleware\TrustProxies.php(39): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#51 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Http\Middleware\TrustProxies->handle(Object(Illuminate\Http\Request), Object(Closure))
#52 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(103): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#53 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(142): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#54 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(111): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#55 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#56 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\server.php(21): require_once('C:\\Users\\<USER>\\...')
#57 {main}  
[2025-06-04 21:30:22] local.INFO: Total invoices found: 242  
[2025-06-04 21:30:22] local.INFO: Formatted data count: 242  
[2025-06-04 21:30:29] local.INFO: Displaying invoice #110 of type payment  
[2025-06-04 21:39:16] local.INFO: Displaying invoice #110 of type payment  
[2025-06-04 21:40:59] local.INFO: Displaying invoice #110 of type payment  
[2025-06-04 21:44:39] local.INFO: Displaying invoice #110 of type payment  
[2025-06-04 21:45:45] local.INFO: Displaying invoice #110 of type payment  
[2025-06-04 21:46:02] local.INFO: Displaying invoice #110 of type payment  
[2025-06-04 21:48:20] local.INFO: Displaying invoice #110 of type payment  
[2025-06-04 21:50:01] local.ERROR: ErrorException: Trying to access array offset on null in C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\app\Http\Controllers\Datatables\MobileUserDatatable.php:47
Stack trace:
#0 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\app\Http\Controllers\Datatables\MobileUserDatatable.php(47): Illuminate\Foundation\Bootstrap\HandleExceptions->handleError(2, 'Trying to acces...', 'C:\\Users\\<USER>\\...', 47)
#1 [internal function]: App\Http\Controllers\Datatables\MobileUserDatatable->App\Http\Controllers\Datatables\{closure}(Object(Illuminate\Database\Eloquent\Builder))
#2 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\yajra\laravel-datatables-oracle\src\DataTableAbstract.php(700): call_user_func(Object(Closure), Object(Illuminate\Database\Eloquent\Builder))
#3 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\yajra\laravel-datatables-oracle\src\QueryDataTable.php(142): Yajra\DataTables\DataTableAbstract->filterRecords()
#4 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\yajra\laravel-datatables-oracle\src\QueryDataTable.php(99): Yajra\DataTables\QueryDataTable->prepareQuery()
#5 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\app\Http\Controllers\Datatables\MobileUserDatatable.php(56): Yajra\DataTables\QueryDataTable->make(true)
#6 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Controller.php(54): App\Http\Controllers\Datatables\MobileUserDatatable->mobileUsersData(Object(Illuminate\Http\Request))
#7 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php(45): Illuminate\Routing\Controller->callAction('mobileUsersData', Array)
#8 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Route.php(261): Illuminate\Routing\ControllerDispatcher->dispatch(Object(Illuminate\Routing\Route), Object(App\Http\Controllers\Datatables\MobileUserDatatable), 'mobileUsersData')
#9 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Route.php(205): Illuminate\Routing\Route->runController()
#10 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Router.php(721): Illuminate\Routing\Route->run()
#11 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(128): Illuminate\Routing\Router->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#12 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\app\Http\Middleware\AdminMiddleware.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#13 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\AdminMiddleware->handle(Object(Illuminate\Http\Request), Object(Closure))
#14 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(50): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#15 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Routing\Middleware\SubstituteBindings->handle(Object(Illuminate\Http\Request), Object(Closure))
#16 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Auth\Middleware\Authenticate.php(44): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#17 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Auth\Middleware\Authenticate->handle(Object(Illuminate\Http\Request), Object(Closure))
#18 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php(78): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#19 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\VerifyCsrfToken->handle(Object(Illuminate\Http\Request), Object(Closure))
#20 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\View\Middleware\ShareErrorsFromSession.php(49): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#21 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\View\Middleware\ShareErrorsFromSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#22 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(121): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#23 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(64): Illuminate\Session\Middleware\StartSession->handleStatefulRequest(Object(Illuminate\Http\Request), Object(Illuminate\Session\Store), Object(Closure))
#24 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Session\Middleware\StartSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#25 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse.php(37): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#26 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse->handle(Object(Illuminate\Http\Request), Object(Closure))
#27 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\EncryptCookies.php(67): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#28 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Cookie\Middleware\EncryptCookies->handle(Object(Illuminate\Http\Request), Object(Closure))
#29 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(103): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#30 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Router.php(719): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#31 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Router.php(698): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request))
#32 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Router.php(662): Illuminate\Routing\Router->runRoute(Object(Illuminate\Http\Request), Object(Illuminate\Routing\Route))
#33 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Router.php(651): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#34 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(167): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#35 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(128): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#36 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\livewire\livewire\src\DisableBrowserCache.php(19): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#37 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Livewire\DisableBrowserCache->handle(Object(Illuminate\Http\Request), Object(Closure))
#38 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#39 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#40 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#41 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#42 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#43 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#44 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#45 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#46 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(86): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#47 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#48 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\barryvdh\laravel-cors\src\HandleCors.php(52): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#49 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Fruitcake\Cors\HandleCors->handle(Object(Illuminate\Http\Request), Object(Closure))
#50 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Http\Middleware\TrustProxies.php(39): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#51 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Http\Middleware\TrustProxies->handle(Object(Illuminate\Http\Request), Object(Closure))
#52 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(103): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#53 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(142): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#54 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(111): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#55 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#56 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\server.php(21): require_once('C:\\Users\\<USER>\\...')
#57 {main}  
[2025-06-04 21:50:01] local.INFO: Total invoices found: 242  
[2025-06-04 21:50:01] local.INFO: Formatted data count: 242  
[2025-06-04 22:31:33] local.ERROR: ErrorException: Trying to access array offset on null in C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\app\Http\Controllers\Datatables\MobileUserDatatable.php:47
Stack trace:
#0 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\app\Http\Controllers\Datatables\MobileUserDatatable.php(47): Illuminate\Foundation\Bootstrap\HandleExceptions->handleError(2, 'Trying to acces...', 'C:\\Users\\<USER>\\...', 47)
#1 [internal function]: App\Http\Controllers\Datatables\MobileUserDatatable->App\Http\Controllers\Datatables\{closure}(Object(Illuminate\Database\Eloquent\Builder))
#2 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\yajra\laravel-datatables-oracle\src\DataTableAbstract.php(700): call_user_func(Object(Closure), Object(Illuminate\Database\Eloquent\Builder))
#3 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\yajra\laravel-datatables-oracle\src\QueryDataTable.php(142): Yajra\DataTables\DataTableAbstract->filterRecords()
#4 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\yajra\laravel-datatables-oracle\src\QueryDataTable.php(99): Yajra\DataTables\QueryDataTable->prepareQuery()
#5 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\app\Http\Controllers\Datatables\MobileUserDatatable.php(56): Yajra\DataTables\QueryDataTable->make(true)
#6 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Controller.php(54): App\Http\Controllers\Datatables\MobileUserDatatable->mobileUsersData(Object(Illuminate\Http\Request))
#7 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php(45): Illuminate\Routing\Controller->callAction('mobileUsersData', Array)
#8 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Route.php(261): Illuminate\Routing\ControllerDispatcher->dispatch(Object(Illuminate\Routing\Route), Object(App\Http\Controllers\Datatables\MobileUserDatatable), 'mobileUsersData')
#9 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Route.php(205): Illuminate\Routing\Route->runController()
#10 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Router.php(721): Illuminate\Routing\Route->run()
#11 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(128): Illuminate\Routing\Router->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#12 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\app\Http\Middleware\AdminMiddleware.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#13 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\AdminMiddleware->handle(Object(Illuminate\Http\Request), Object(Closure))
#14 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(50): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#15 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Routing\Middleware\SubstituteBindings->handle(Object(Illuminate\Http\Request), Object(Closure))
#16 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Auth\Middleware\Authenticate.php(44): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#17 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Auth\Middleware\Authenticate->handle(Object(Illuminate\Http\Request), Object(Closure))
#18 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php(78): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#19 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\VerifyCsrfToken->handle(Object(Illuminate\Http\Request), Object(Closure))
#20 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\View\Middleware\ShareErrorsFromSession.php(49): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#21 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\View\Middleware\ShareErrorsFromSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#22 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(121): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#23 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(64): Illuminate\Session\Middleware\StartSession->handleStatefulRequest(Object(Illuminate\Http\Request), Object(Illuminate\Session\Store), Object(Closure))
#24 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Session\Middleware\StartSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#25 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse.php(37): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#26 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse->handle(Object(Illuminate\Http\Request), Object(Closure))
#27 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\EncryptCookies.php(67): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#28 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Cookie\Middleware\EncryptCookies->handle(Object(Illuminate\Http\Request), Object(Closure))
#29 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(103): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#30 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Router.php(719): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#31 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Router.php(698): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request))
#32 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Router.php(662): Illuminate\Routing\Router->runRoute(Object(Illuminate\Http\Request), Object(Illuminate\Routing\Route))
#33 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Router.php(651): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#34 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(167): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#35 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(128): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#36 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\livewire\livewire\src\DisableBrowserCache.php(19): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#37 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Livewire\DisableBrowserCache->handle(Object(Illuminate\Http\Request), Object(Closure))
#38 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#39 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#40 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#41 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#42 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#43 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#44 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#45 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#46 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(86): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#47 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#48 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\barryvdh\laravel-cors\src\HandleCors.php(52): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#49 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Fruitcake\Cors\HandleCors->handle(Object(Illuminate\Http\Request), Object(Closure))
#50 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Http\Middleware\TrustProxies.php(39): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#51 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Http\Middleware\TrustProxies->handle(Object(Illuminate\Http\Request), Object(Closure))
#52 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(103): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#53 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(142): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#54 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(111): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#55 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#56 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\server.php(21): require_once('C:\\Users\\<USER>\\...')
#57 {main}  
[2025-06-04 22:31:33] local.INFO: Total invoices found: 242  
[2025-06-04 22:31:33] local.INFO: Formatted data count: 242  
[2025-06-04 22:31:39] local.INFO: Displaying invoice #245 of type escrow  
