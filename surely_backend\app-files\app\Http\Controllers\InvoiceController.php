<?php

namespace App\Http\Controllers;

use App\Models\Contract;
use App\Models\Invoice;
use App\Models\MobileUser;
use Illuminate\Http\Request;

class InvoiceController extends Controller
{
    /**
     * Display a listing of all invoices for admin.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        // We'll load the data via AJAX in the datatable
        return view('invoices.index');
    }

    /**
     * Get all invoices data for datatable
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getInvoicesData(Request $request)
    {
        // Query invoices directly instead of going through contracts
        $query = Invoice::with(['contract.client', 'contract.operative']);

        // Apply filters if provided
        if ($request->filled('user_id')) {
            $userId = $request->get('user_id');
            $query->where(function($q) use ($userId) {
                $q->where('client_id', $userId)
                  ->orWhere('operative_id', $userId);
            });
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->get('date_from'));
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->get('date_to'));
        }

        $invoices = $query->orderBy('created_at', 'DESC')->get();

        // Debug information
        \Log::info('Total invoices found: ' . $invoices->count());

        $data = [];
        foreach ($invoices as $invoice) {
            // Skip invoices without a contract
            if (!$invoice->contract) {
                \Log::info('Skipping invoice #' . $invoice->id . ' - no contract found');
                continue;
            }

            // Get client and operative information from the contract
            $client = $invoice->contract->client;
            $operative = $invoice->contract->operative;

            // Handle potentially missing values safely
            $data[] = [
                'invoice_id' => $invoice->id,
                'contract_id' => $invoice->contract_id,
                'client_name' => $client ? $client->name : 'N/A',
                'client_email' => $client ? $client->email : 'N/A',
                'operative_name' => $operative ? $operative->name : 'N/A',
                'operative_email' => $operative ? $operative->email : 'N/A',
                'amount' => $invoice->amount ?? 0,
                'currency' => $invoice->currency ?? 'GBP',
                'type' => $invoice->type,
                'contract_start_date' => $invoice->contract ? $invoice->contract->getStartDate() : null,
                'contract_end_date' => $invoice->contract ? $invoice->contract->getEndDate() : null,
                // Calculate payment due date: end_date + payment_terms days (default 10)
                'payment_terms' => $invoice->contract && ($endDate = $invoice->contract->getEndDate()) ? 
                    date('Y-m-d', strtotime($endDate . ' + ' . ($invoice->contract->payment_terms ?? 10) . ' days')) : 
                    null,
                'payment_status' => $invoice->status ?? 'pending',
            ];
        }

        \Log::info('Formatted data count: ' . count($data));
        return response()->json(['data' => $data]);
    }

    /**
     * Display the details of a specific invoice
     *
     * @param int $id
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        // Find the invoice by ID
        $invoice = Invoice::findOrFail($id);
        
        // Check if contract exists
        if (!$invoice->contract_id) {
            return redirect()->route('invoices.index')
                ->with('error', 'This invoice is not associated with a contract!');
        }
        
        $contract = Contract::with(['client', 'operative', 'job'])->find($invoice->contract_id);
        
        if (!$contract) {
            return redirect()->route('invoices.index')
                ->with('error', 'The contract associated with this invoice could not be found!');
        }

        // Check if client exists
        if (!$contract->client) {
            return redirect()->route('invoices.index')
                ->with('error', 'The client associated with this contract could not be found!');
        }
        
        // Determine if this is an escrow or payment invoice
        if ($invoice->type == Invoice::TYPE_ESCROW) {
            $invoiceEscrow = $invoice;
            $invoiceOutstanding = Invoice::where('contract_id', $contract->id)
                ->where('type', Invoice::TYPE_PAYMENT)
                ->first();
        } else {
            $invoiceOutstanding = $invoice;
            $invoiceEscrow = Invoice::where('contract_id', $contract->id)
                ->where('type', Invoice::TYPE_ESCROW)
                ->first();
        }

        // If we're viewing a payment invoice but no escrow invoice exists
        if (!$invoiceEscrow && $invoice->type == Invoice::TYPE_PAYMENT) {
            $invoiceEscrow = $invoice; // Just use the current invoice for display
        }

        // Calculate due date
        $dueDate = clone $invoice->created_at;
        $paymentTerms = $contract->payment_terms ?? 10;

        if ($paymentTerms) {
            $dueDate->modify('+' . $paymentTerms . ' days');
        } else {
            $dueDate->modify('+10 days');
        }

        // Log what we're about to display
        \Log::info('Displaying invoice #' . $invoice->id . ' of type ' . $invoice->type);

        return view('invoices.show', [
            'invoice' => $invoice,
            'contract' => $contract,
            'invoiceEscrow' => $invoiceEscrow,
            'invoiceOutstanding' => $invoiceOutstanding,
            'dueDate' => $dueDate->format('d-m-Y'),
            'issueDate' => $invoice->created_at->format('d-m-Y'),
            // Add additional fields with null checks
            'sub_amount' => $invoice->sub_amount ?? 0,
            'application_fee_amount' => $invoice->application_fee_amount ?? 0,
            'application_fee_rate' => $invoice->application_fee_rate ?? 0,
            'operator_vat_amount' => $invoice->operator_vat_amount ?? 0,
            'operator_vat_rate' => $invoice->operator_vat_rate ?? 0,
            'application_vat_amount' => $invoice->application_vat_amount ?? 0,
            'application_vat_rate' => $invoice->application_vat_rate ?? 0,
            'payment_status' => $invoice->payment_status ?? 'pending',
        ]);
    }

    /**
     * Debug page to diagnose invoice data issues
     *
     * @return \Illuminate\View\View
     */
    public function debug()
    {
        // Get all invoices
        $allInvoices = Invoice::all();
        $totalInvoices = $allInvoices->count();
        
        // Count invoices with contracts
        $invoicesWithContracts = $allInvoices->filter(function($invoice) {
            return $invoice->contract_id && $invoice->contract;
        })->count();
        
        // Get sample of first 10 invoices
        $sampleInvoices = $allInvoices->take(10);
        
        // Get formatted data for DataTable
        $formattedData = [];
        
        foreach ($allInvoices as $invoice) {
            // Get contract and related users
            $contract = null;
            $client = null;
            $operative = null;
            
            if ($invoice->contract_id) {
                $contract = Contract::with(['client', 'operative', 'job'])->find($invoice->contract_id);
                if ($contract) {
                    $client = $contract->client;
                    $operative = $contract->operative;
                }
            }
            
            // Format the data
            $formattedData[] = [
                'invoice_id' => $invoice->id,
                'contract_id' => $invoice->contract_id ?? 'N/A',
                'client_name' => $client ? $client->name : 'N/A',
                'client_email' => $client ? $client->email : 'N/A',
                'operative_name' => $operative ? $operative->name : 'N/A',
                'operative_email' => $operative ? $operative->email : 'N/A',
                'amount' => $invoice->amount,
                'currency' => $invoice->currency ?? 'GBP',
                'sub_amount' => $invoice->sub_amount ?? 0,
                'fee_amount' => $invoice->application_fee_amount ?? 0,
                'vat_amount' => ($invoice->operator_vat_amount ?? 0) + ($invoice->application_vat_amount ?? 0),
                'type' => $invoice->type,
                'status' => $invoice->status,
                'payment_status' => $invoice->payment_status ?? 'pending',
                'created_at' => $invoice->created_at->format('Y-m-d'),
                'view_url' => route('invoices.show', $invoice->id)
            ];
        }
        
        return view('invoices.debug', [
            'totalInvoices' => $totalInvoices,
            'invoicesWithContracts' => $invoicesWithContracts,
            'sampleInvoices' => $sampleInvoices,
            'formattedData' => $formattedData
        ]);
    }
}
