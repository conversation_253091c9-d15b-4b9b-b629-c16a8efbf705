[2025-06-05 07:03:36] local.ERROR: ErrorException: Trying to access array offset on null in C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\app\Http\Controllers\Datatables\MobileUserDatatable.php:47
Stack trace:
#0 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\app\Http\Controllers\Datatables\MobileUserDatatable.php(47): Illuminate\Foundation\Bootstrap\HandleExceptions->handleError(2, 'Trying to acces...', 'C:\\Users\\<USER>\\...', 47)
#1 [internal function]: App\Http\Controllers\Datatables\MobileUserDatatable->App\Http\Controllers\Datatables\{closure}(Object(Illuminate\Database\Eloquent\Builder))
#2 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\yajra\laravel-datatables-oracle\src\DataTableAbstract.php(700): call_user_func(Object(Closure), Object(Illuminate\Database\Eloquent\Builder))
#3 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\yajra\laravel-datatables-oracle\src\QueryDataTable.php(142): Yajra\DataTables\DataTableAbstract->filterRecords()
#4 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\yajra\laravel-datatables-oracle\src\QueryDataTable.php(99): Yajra\DataTables\QueryDataTable->prepareQuery()
#5 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\app\Http\Controllers\Datatables\MobileUserDatatable.php(56): Yajra\DataTables\QueryDataTable->make(true)
#6 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Controller.php(54): App\Http\Controllers\Datatables\MobileUserDatatable->mobileUsersData(Object(Illuminate\Http\Request))
#7 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php(45): Illuminate\Routing\Controller->callAction('mobileUsersData', Array)
#8 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Route.php(261): Illuminate\Routing\ControllerDispatcher->dispatch(Object(Illuminate\Routing\Route), Object(App\Http\Controllers\Datatables\MobileUserDatatable), 'mobileUsersData')
#9 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Route.php(205): Illuminate\Routing\Route->runController()
#10 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Router.php(721): Illuminate\Routing\Route->run()
#11 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(128): Illuminate\Routing\Router->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#12 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\app\Http\Middleware\AdminMiddleware.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#13 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\AdminMiddleware->handle(Object(Illuminate\Http\Request), Object(Closure))
#14 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(50): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#15 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Routing\Middleware\SubstituteBindings->handle(Object(Illuminate\Http\Request), Object(Closure))
#16 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Auth\Middleware\Authenticate.php(44): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#17 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Auth\Middleware\Authenticate->handle(Object(Illuminate\Http\Request), Object(Closure))
#18 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php(78): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#19 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\VerifyCsrfToken->handle(Object(Illuminate\Http\Request), Object(Closure))
#20 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\View\Middleware\ShareErrorsFromSession.php(49): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#21 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\View\Middleware\ShareErrorsFromSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#22 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(121): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#23 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(64): Illuminate\Session\Middleware\StartSession->handleStatefulRequest(Object(Illuminate\Http\Request), Object(Illuminate\Session\Store), Object(Closure))
#24 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Session\Middleware\StartSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#25 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse.php(37): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#26 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse->handle(Object(Illuminate\Http\Request), Object(Closure))
#27 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\EncryptCookies.php(67): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#28 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Cookie\Middleware\EncryptCookies->handle(Object(Illuminate\Http\Request), Object(Closure))
#29 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(103): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#30 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Router.php(719): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#31 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Router.php(698): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request))
#32 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Router.php(662): Illuminate\Routing\Router->runRoute(Object(Illuminate\Http\Request), Object(Illuminate\Routing\Route))
#33 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Router.php(651): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#34 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(167): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#35 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(128): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#36 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\livewire\livewire\src\DisableBrowserCache.php(19): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#37 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Livewire\DisableBrowserCache->handle(Object(Illuminate\Http\Request), Object(Closure))
#38 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#39 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#40 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#41 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#42 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#43 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#44 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#45 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#46 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(86): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#47 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#48 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\barryvdh\laravel-cors\src\HandleCors.php(52): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#49 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Fruitcake\Cors\HandleCors->handle(Object(Illuminate\Http\Request), Object(Closure))
#50 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Http\Middleware\TrustProxies.php(39): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#51 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Http\Middleware\TrustProxies->handle(Object(Illuminate\Http\Request), Object(Closure))
#52 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(103): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#53 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(142): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#54 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(111): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#55 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#56 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\server.php(21): require_once('C:\\Users\\<USER>\\...')
#57 {main}  
[2025-06-05 07:03:36] local.INFO: Total invoices found: 242  
[2025-06-05 07:03:36] local.INFO: Formatted data count: 242  
[2025-06-05 07:03:43] local.INFO: Displaying invoice #245 of type escrow  
[2025-06-05 07:10:51] local.INFO: Broadcasting [App\Events\NewMessageEvent] on channels [surely-development] with payload:
{
    "message": {
        "id": 3253,
        "chat_id": 280,
        "sender_id": 822,
        "receiver_id": 823,
        "job_id": 309,
        "contract_id": 258,
        "payment_id": null,
        "message": "Hourly rate changed",
        "read": null,
        "type": "hourly_rate_changed",
        "created_at": "2025-06-05T07:10:50.000000Z",
        "updated_at": "2025-06-05T07:10:50.000000Z",
        "deleted_at": null,
        "receiver": {
            "id": 823,
            "name": "Leo Bianchi",
            "email": "<EMAIL>",
            "phone": "************",
            "status": 1,
            "login_type": "1",
            "platform": "3",
            "social_id": null,
            "account_type": "1",
            "firebase_token": "test",
            "user_update_id": null,
            "created_at": "2024-07-17T16:59:39.000000Z",
            "updated_at": "2025-04-09T18:14:04.000000Z",
            "deleted_at": null,
            "age": null,
            "height": null,
            "gender": null,
            "pay_rate": null,
            "years_of_experience": null,
            "company_registration": null,
            "expertise_id": null,
            "address_1": "London",
            "address_2": null,
            "postal_code": "NW5 3DG",
            "website": null,
            "app_version": "web 1.0",
            "lat": "51.547335",
            "lng": "-0.142343",
            "address": "123 high street",
            "city_id": null,
            "profile_photo": null,
            "profile_video": null,
            "additional_pictures": null,
            "profile_title": "My bio",
            "profile_description": "Bio here",
            "instant_book": 0,
            "account_name": null,
            "sort_code": "108800",
            "account_number": "********",
            "utr_number": null,
            "vat_number": null,
            "company_number": null,
            "location_range": 50,
            "sia_licence_number": null,
            "sia_licence_expiry_date": null,
            "sia_licence_card_photo": null,
            "document_type": null,
            "id_front_document": null,
            "id_back_document": null,
            "address_verification_document": null,
            "selfie_verification_document": null,
            "client_represent_business_as": null,
            "company_registered_number": null,
            "sia_licence_types": [],
            "industry_sectors": [
                "Bars, Clubs & Restaurants"
            ],
            "address_3": null,
            "city": "Camden, London, England",
            "attempts": 0,
            "email_code": null,
            "email_code_expires_at": null,
            "sent_at": null,
            "code": null,
            "email_token": null,
            "email_verified": 1,
            "banned": 0,
            "banned_timestamp": null,
            "operator_vat": null,
            "referal_code": "fe5671",
            "referal_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzM4NCJ9.***********************************************************************************************************************************************************.0M2zBdI0IJI7xciqxd6UbP80rKDu-xFCm0C_ZfL_hyRSVjxJidVWrWVZ2OJAv6TV",
            "emergency_hire_notification_status": 0,
            "email_verification_deadline": null,
            "is_guest": false
        }
    },
    "socket": null
}  
[2025-06-05 07:11:25] local.DEBUG: Message-ID: <5b36867a3e5a22aa776f9881a9ec55ef@127.0.0.1>
Date: Thu, 05 Jun 2025 07:11:25 +0000
Subject: URGENT: New Message for Emergency Hire Job
From: Surely <<EMAIL>>
To: <EMAIL>
MIME-Version: 1.0
Content-Type: text/html; charset=utf-8
Content-Transfer-Encoding: quoted-printable

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>URGENT: New Message for Emergency Hire Job</title>
</head>
<body>
    <p>Dear Leo Bianchi,</p>

    <p><strong>URGENT:</strong> You have received a new message regarding an <strong>Emergency Hire</strong> job that requires immediate attention.</p>

    <p><strong>Job Details:</strong></p>
    <ul>
        <li><strong>Title:</strong> payment test</li>
        <li><strong>Location:</strong> B1 1DB</li>
    </ul>

    <p><strong>Message from Olivia Brown:</strong></p>
    <p style="background-color: #f8f9fa; padding: 10px; border-left: 4px solid #dc3545;">Escrow payment of £130.2 has been added to the contract.</p>

    <p><a href="http://localhost:5173/chat/280" style="display: inline-block; background-color: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; font-weight: bold;">View Conversation</a></p>

    <p>Please respond promptly as this is an emergency hire job.</p>

    <p>Thank you for using Surely Security!</p>

    <p>Best regards,<br>
    Aliona & Aurela</p>
</body>
</html>
  
[2025-06-05 07:11:25] local.INFO: Sent emergency hire message <NAME_EMAIL> for job #309  
[2025-06-05 07:11:25] local.INFO: Broadcasting [App\Events\NewMessageEvent] on channels [surely-development] with payload:
{
    "message": {
        "id": 3255,
        "chat_id": 280,
        "sender_id": 822,
        "receiver_id": 823,
        "job_id": null,
        "contract_id": null,
        "payment_id": null,
        "message": "Escrow payment of \u00a3130.2 has been added to the contract.",
        "read": null,
        "type": "payment_status",
        "created_at": "2025-06-05T07:11:25.000000Z",
        "updated_at": "2025-06-05T07:11:25.000000Z",
        "deleted_at": null,
        "receiver": {
            "id": 823,
            "name": "Leo Bianchi",
            "email": "<EMAIL>",
            "phone": "************",
            "status": 1,
            "login_type": "1",
            "platform": "3",
            "social_id": null,
            "account_type": "1",
            "firebase_token": "test",
            "user_update_id": null,
            "created_at": "2024-07-17T16:59:39.000000Z",
            "updated_at": "2025-04-09T18:14:04.000000Z",
            "deleted_at": null,
            "age": null,
            "height": null,
            "gender": null,
            "pay_rate": null,
            "years_of_experience": null,
            "company_registration": null,
            "expertise_id": null,
            "address_1": "London",
            "address_2": null,
            "postal_code": "NW5 3DG",
            "website": null,
            "app_version": "web 1.0",
            "lat": "51.547335",
            "lng": "-0.142343",
            "address": "123 high street",
            "city_id": null,
            "profile_photo": null,
            "profile_video": null,
            "additional_pictures": null,
            "profile_title": "My bio",
            "profile_description": "Bio here",
            "instant_book": 0,
            "account_name": null,
            "sort_code": "108800",
            "account_number": "********",
            "utr_number": null,
            "vat_number": null,
            "company_number": null,
            "location_range": 50,
            "sia_licence_number": null,
            "sia_licence_expiry_date": null,
            "sia_licence_card_photo": null,
            "document_type": null,
            "id_front_document": null,
            "id_back_document": null,
            "address_verification_document": null,
            "selfie_verification_document": null,
            "client_represent_business_as": null,
            "company_registered_number": null,
            "sia_licence_types": [],
            "industry_sectors": [
                "Bars, Clubs & Restaurants"
            ],
            "address_3": null,
            "city": "Camden, London, England",
            "attempts": 0,
            "email_code": null,
            "email_code_expires_at": null,
            "sent_at": null,
            "code": null,
            "email_token": null,
            "email_verified": 1,
            "banned": 0,
            "banned_timestamp": null,
            "operator_vat": null,
            "referal_code": "fe5671",
            "referal_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzM4NCJ9.***********************************************************************************************************************************************************.0M2zBdI0IJI7xciqxd6UbP80rKDu-xFCm0C_ZfL_hyRSVjxJidVWrWVZ2OJAv6TV",
            "emergency_hire_notification_status": 0,
            "email_verification_deadline": null,
            "is_guest": false
        },
        "sender": {
            "id": 822,
            "name": "Olivia Brown",
            "email": "<EMAIL>",
            "phone": "************",
            "status": 1,
            "login_type": "1",
            "platform": "3",
            "social_id": null,
            "account_type": "2",
            "firebase_token": "test",
            "user_update_id": null,
            "created_at": "2024-07-17T16:42:59.000000Z",
            "updated_at": "2024-07-17T16:44:26.000000Z",
            "deleted_at": null,
            "age": null,
            "height": null,
            "gender": null,
            "pay_rate": null,
            "years_of_experience": null,
            "company_registration": null,
            "expertise_id": null,
            "address_1": null,
            "address_2": "Kensington and Chelsea, London, England",
            "postal_code": "SW35qp",
            "website": null,
            "app_version": "web 1.0",
            "lat": "51.485292",
            "lng": "-0.165607",
            "address": null,
            "city_id": null,
            "profile_photo": null,
            "profile_video": null,
            "additional_pictures": null,
            "profile_title": null,
            "profile_description": null,
            "instant_book": 0,
            "account_name": null,
            "sort_code": null,
            "account_number": null,
            "utr_number": null,
            "vat_number": null,
            "company_number": null,
            "location_range": null,
            "sia_licence_number": null,
            "sia_licence_expiry_date": null,
            "sia_licence_card_photo": null,
            "document_type": null,
            "id_front_document": null,
            "id_back_document": null,
            "address_verification_document": null,
            "selfie_verification_document": null,
            "client_represent_business_as": "Private Individual",
            "company_registered_number": null,
            "sia_licence_types": null,
            "industry_sectors": [],
            "address_3": null,
            "city": "Kensington and Chelsea, London, England",
            "attempts": 0,
            "email_code": null,
            "email_code_expires_at": null,
            "sent_at": null,
            "code": null,
            "email_token": null,
            "email_verified": 1,
            "banned": 0,
            "banned_timestamp": null,
            "operator_vat": null,
            "referal_code": "68bef7",
            "referal_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzM4NCJ9.****************************************************************************************************************************************************************.GymkqHyBVpHQd59AfaDBiK06s9ra7q8PWINxj1JwnWW73c7lRq5PPKAx9rwSMvzh",
            "emergency_hire_notification_status": 0,
            "email_verification_deadline": null,
            "is_guest": false
        }
    },
    "socket": null
}  
[2025-06-05 07:11:38] local.ERROR: ErrorException: Trying to access array offset on null in C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\app\Http\Controllers\Datatables\MobileUserDatatable.php:47
Stack trace:
#0 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\app\Http\Controllers\Datatables\MobileUserDatatable.php(47): Illuminate\Foundation\Bootstrap\HandleExceptions->handleError(2, 'Trying to acces...', 'C:\\Users\\<USER>\\...', 47)
#1 [internal function]: App\Http\Controllers\Datatables\MobileUserDatatable->App\Http\Controllers\Datatables\{closure}(Object(Illuminate\Database\Eloquent\Builder))
#2 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\yajra\laravel-datatables-oracle\src\DataTableAbstract.php(700): call_user_func(Object(Closure), Object(Illuminate\Database\Eloquent\Builder))
#3 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\yajra\laravel-datatables-oracle\src\QueryDataTable.php(142): Yajra\DataTables\DataTableAbstract->filterRecords()
#4 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\yajra\laravel-datatables-oracle\src\QueryDataTable.php(99): Yajra\DataTables\QueryDataTable->prepareQuery()
#5 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\app\Http\Controllers\Datatables\MobileUserDatatable.php(56): Yajra\DataTables\QueryDataTable->make(true)
#6 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Controller.php(54): App\Http\Controllers\Datatables\MobileUserDatatable->mobileUsersData(Object(Illuminate\Http\Request))
#7 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php(45): Illuminate\Routing\Controller->callAction('mobileUsersData', Array)
#8 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Route.php(261): Illuminate\Routing\ControllerDispatcher->dispatch(Object(Illuminate\Routing\Route), Object(App\Http\Controllers\Datatables\MobileUserDatatable), 'mobileUsersData')
#9 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Route.php(205): Illuminate\Routing\Route->runController()
#10 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Router.php(721): Illuminate\Routing\Route->run()
#11 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(128): Illuminate\Routing\Router->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#12 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\app\Http\Middleware\AdminMiddleware.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#13 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): App\Http\Middleware\AdminMiddleware->handle(Object(Illuminate\Http\Request), Object(Closure))
#14 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(50): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#15 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Routing\Middleware\SubstituteBindings->handle(Object(Illuminate\Http\Request), Object(Closure))
#16 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Auth\Middleware\Authenticate.php(44): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#17 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Auth\Middleware\Authenticate->handle(Object(Illuminate\Http\Request), Object(Closure))
#18 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\VerifyCsrfToken.php(78): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#19 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\VerifyCsrfToken->handle(Object(Illuminate\Http\Request), Object(Closure))
#20 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\View\Middleware\ShareErrorsFromSession.php(49): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#21 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\View\Middleware\ShareErrorsFromSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#22 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(121): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#23 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Session\Middleware\StartSession.php(64): Illuminate\Session\Middleware\StartSession->handleStatefulRequest(Object(Illuminate\Http\Request), Object(Illuminate\Session\Store), Object(Closure))
#24 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Session\Middleware\StartSession->handle(Object(Illuminate\Http\Request), Object(Closure))
#25 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse.php(37): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#26 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse->handle(Object(Illuminate\Http\Request), Object(Closure))
#27 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Cookie\Middleware\EncryptCookies.php(67): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#28 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Cookie\Middleware\EncryptCookies->handle(Object(Illuminate\Http\Request), Object(Closure))
#29 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(103): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#30 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Router.php(719): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#31 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Router.php(698): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request))
#32 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Router.php(662): Illuminate\Routing\Router->runRoute(Object(Illuminate\Http\Request), Object(Illuminate\Routing\Route))
#33 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Routing\Router.php(651): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#34 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(167): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#35 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(128): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#36 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\livewire\livewire\src\DisableBrowserCache.php(19): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#37 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Livewire\DisableBrowserCache->handle(Object(Illuminate\Http\Request), Object(Closure))
#38 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#39 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull.php(31): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#40 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull->handle(Object(Illuminate\Http\Request), Object(Closure))
#41 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(21): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#42 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TrimStrings.php(40): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#43 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\TrimStrings->handle(Object(Illuminate\Http\Request), Object(Closure))
#44 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#45 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#46 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance.php(86): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#47 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Foundation\Http\Middleware\PreventRequestsDuringMaintenance->handle(Object(Illuminate\Http\Request), Object(Closure))
#48 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\barryvdh\laravel-cors\src\HandleCors.php(52): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#49 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Fruitcake\Cors\HandleCors->handle(Object(Illuminate\Http\Request), Object(Closure))
#50 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Http\Middleware\TrustProxies.php(39): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#51 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(167): Illuminate\Http\Middleware\TrustProxies->handle(Object(Illuminate\Http\Request), Object(Closure))
#52 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(103): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#53 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(142): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#54 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(111): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#55 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\public\index.php(51): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#56 C:\Users\<USER>\GitHub_LocalRespository\surely-server\surely_backend\app-files\server.php(21): require_once('C:\\Users\\<USER>\\...')
#57 {main}  
[2025-06-05 07:11:38] local.INFO: Total invoices found: 243  
[2025-06-05 07:11:38] local.INFO: Formatted data count: 243  
[2025-06-05 07:11:42] local.INFO: Displaying invoice #246 of type escrow  
[2025-06-05 07:43:33] local.INFO: Displaying invoice #246 of type escrow  
